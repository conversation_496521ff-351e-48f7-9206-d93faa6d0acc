/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.AccountTeamMember;
import com.app.cargill.crescendo.service.ICrescendoAccountTeamMemberService;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@SuppressWarnings(
    "java:S1172") // Added for unused variables, which will be incorporated during translations
// integration
public class CrescendoAccountTeamMemberServiceImpl implements ICrescendoAccountTeamMemberService {

  private final CrescendoApiServiceImpl crescendoApiServiceImpl;

  @Override
  public String createAccountTeamMember(
      AccountTeamMember accountTeamMember, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = crescendoApiServiceImpl.getTokenAndApiPath();
    return createAccountTeamMember(
        token.getApiPath(), token.getAuthToken(), accountTeamMember, source, locale);
  }

  private String createAccountTeamMember(
      String apiPath,
      AuthToken authToken,
      AccountTeamMember accountTeamMember,
      ResourceBundleMessageSource source,
      Locale locale)
      throws CustomDEExceptions {
    try {
      String accountTeamMemberUrl = String.format("%s/sobjects/AccountTeamMember", apiPath);

      log.info("CREATED CRESCENDO ACCOUNT TEAM MEMBER STARTED {}", accountTeamMember);
      CreateRecordResponse recordResponse =
          crescendoApiServiceImpl.createRecord(
              authToken,
              accountTeamMember,
              new ParameterizedTypeReference<>() {},
              accountTeamMemberUrl);
      log.info("CRESCENDO_ACCOUNT_TEAM_MEMBER_CREATED {}", recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception ex) {
      log.error(
          "CRESCENDO_ACCOUNT_TEAM_MEMBER_CREATE_ERROR {} , {}",
          accountTeamMember.getId(),
          ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }
}
