/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class AccountTeamMemberTest {

  @Test
  void testAccountTeamMemberSerialization() throws JsonProcessingException {
    // Arrange
    AccountTeamMember accountTeamMember = new AccountTeamMember();
    accountTeamMember.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setTeamMemberRole("Account Manager");
    accountTeamMember.setAccountAccessLevel("Edit");
    accountTeamMember.setOpportunityAccessLevel("Edit");
    accountTeamMember.setCaseAccessLevel("Edit");
    accountTeamMember.setContactAccessLevel("Edit");

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    String json = objectMapper.writeValueAsString(accountTeamMember);
    AccountTeamMember deserializedAccountTeamMember =
        objectMapper.readValue(json, AccountTeamMember.class);

    // Assert
    assertNotNull(json);
    assertTrue(json.contains("\"Id\":\"001XXXXXXXXXXXXXXX\""));
    assertTrue(json.contains("\"UserId\":\"005XXXXXXXXXXXXXXX\""));
    assertTrue(json.contains("\"AccountId\":\"001XXXXXXXXXXXXXXX\""));
    assertTrue(json.contains("\"TeamMemberRole\":\"Account Manager\""));
    assertTrue(json.contains("\"AccountAccessLevel\":\"Edit\""));
    assertTrue(json.contains("\"OpportunityAccessLevel\":\"Edit\""));
    assertTrue(json.contains("\"CaseAccessLevel\":\"Edit\""));
    assertTrue(json.contains("\"ContactAccessLevel\":\"Edit\""));

    assertEquals(accountTeamMember.getId(), deserializedAccountTeamMember.getId());
    assertEquals(accountTeamMember.getUserId(), deserializedAccountTeamMember.getUserId());
    assertEquals(accountTeamMember.getAccountId(), deserializedAccountTeamMember.getAccountId());
    assertEquals(
        accountTeamMember.getTeamMemberRole(), deserializedAccountTeamMember.getTeamMemberRole());
    assertEquals(
        accountTeamMember.getAccountAccessLevel(),
        deserializedAccountTeamMember.getAccountAccessLevel());
    assertEquals(
        accountTeamMember.getOpportunityAccessLevel(),
        deserializedAccountTeamMember.getOpportunityAccessLevel());
    assertEquals(
        accountTeamMember.getCaseAccessLevel(), deserializedAccountTeamMember.getCaseAccessLevel());
    assertEquals(
        accountTeamMember.getContactAccessLevel(),
        deserializedAccountTeamMember.getContactAccessLevel());
  }

  @Test
  void testAccountTeamMemberDeserialization() throws JsonProcessingException {
    // Arrange
    String json =
        """
        {
          "Id": "001XXXXXXXXXXXXXXX",
          "UserId": "005XXXXXXXXXXXXXXX",
          "AccountId": "001XXXXXXXXXXXXXXX",
          "TeamMemberRole": "Account Manager",
          "AccountAccessLevel": "Edit",
          "OpportunityAccessLevel": "Edit",
          "CaseAccessLevel": "Edit",
          "ContactAccessLevel": "Edit"
        }
        """;

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    AccountTeamMember accountTeamMember = objectMapper.readValue(json, AccountTeamMember.class);

    // Assert
    assertNotNull(accountTeamMember);
    assertEquals("001XXXXXXXXXXXXXXX", accountTeamMember.getId());
    assertEquals("005XXXXXXXXXXXXXXX", accountTeamMember.getUserId());
    assertEquals("001XXXXXXXXXXXXXXX", accountTeamMember.getAccountId());
    assertEquals("Account Manager", accountTeamMember.getTeamMemberRole());
    assertEquals("Edit", accountTeamMember.getAccountAccessLevel());
    assertEquals("Edit", accountTeamMember.getOpportunityAccessLevel());
    assertEquals("Edit", accountTeamMember.getCaseAccessLevel());
    assertEquals("Edit", accountTeamMember.getContactAccessLevel());
  }

  @Test
  void testAccountTeamMemberWithNullValues() throws JsonProcessingException {
    // Arrange
    AccountTeamMember accountTeamMember = new AccountTeamMember();
    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    String json = objectMapper.writeValueAsString(accountTeamMember);
    AccountTeamMember deserializedAccountTeamMember =
        objectMapper.readValue(json, AccountTeamMember.class);

    // Assert
    assertNotNull(json);
    assertNotNull(deserializedAccountTeamMember);
    assertNull(deserializedAccountTeamMember.getId());
    assertNull(deserializedAccountTeamMember.getUserId());
    assertNull(deserializedAccountTeamMember.getAccountId());
    assertNull(deserializedAccountTeamMember.getTeamMemberRole());
    assertNull(deserializedAccountTeamMember.getAccountAccessLevel());
    assertNull(deserializedAccountTeamMember.getOpportunityAccessLevel());
    assertNull(deserializedAccountTeamMember.getCaseAccessLevel());
    assertNull(deserializedAccountTeamMember.getContactAccessLevel());
  }

  @Test
  void testAccountTeamMemberFieldComparison() {
    // Arrange - Test that objects with same field values have same field values
    AccountTeamMember accountTeamMember1 = new AccountTeamMember();
    accountTeamMember1.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember1.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember1.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember1.setTeamMemberRole("Account Manager");
    accountTeamMember1.setAccountAccessLevel("Edit");
    accountTeamMember1.setOpportunityAccessLevel("Edit");
    accountTeamMember1.setCaseAccessLevel("Edit");
    accountTeamMember1.setContactAccessLevel("Edit");

    AccountTeamMember accountTeamMember2 = new AccountTeamMember();
    accountTeamMember2.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember2.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember2.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember2.setTeamMemberRole("Account Manager");
    accountTeamMember2.setAccountAccessLevel("Edit");
    accountTeamMember2.setOpportunityAccessLevel("Edit");
    accountTeamMember2.setCaseAccessLevel("Edit");
    accountTeamMember2.setContactAccessLevel("Edit");

    AccountTeamMember accountTeamMember3 = new AccountTeamMember();
    accountTeamMember3.setId("002XXXXXXXXXXXXXXX");
    accountTeamMember3.setUserId("006XXXXXXXXXXXXXXX");
    accountTeamMember3.setAccountId("002XXXXXXXXXXXXXXX");
    accountTeamMember3.setTeamMemberRole("Sales Rep");
    accountTeamMember3.setAccountAccessLevel("Read");
    accountTeamMember3.setOpportunityAccessLevel("Read");
    accountTeamMember3.setCaseAccessLevel("Read");
    accountTeamMember3.setContactAccessLevel("Read");

    // Assert - Test field values are correctly set and retrieved
    assertEquals(accountTeamMember1.getId(), accountTeamMember2.getId());
    assertEquals(accountTeamMember1.getUserId(), accountTeamMember2.getUserId());
    assertEquals(accountTeamMember1.getAccountId(), accountTeamMember2.getAccountId());
    assertEquals(accountTeamMember1.getTeamMemberRole(), accountTeamMember2.getTeamMemberRole());

    assertNotEquals(accountTeamMember1.getId(), accountTeamMember3.getId());
    assertNotEquals(accountTeamMember1.getUserId(), accountTeamMember3.getUserId());
    assertNotEquals(accountTeamMember1.getTeamMemberRole(), accountTeamMember3.getTeamMemberRole());

    // Test that objects are not null and have different references
    assertNotNull(accountTeamMember1);
    assertNotNull(accountTeamMember2);
    assertNotSame(accountTeamMember1, accountTeamMember2);
  }

  @Test
  void testAccountTeamMemberToString() {
    // Arrange
    AccountTeamMember accountTeamMember = new AccountTeamMember();
    accountTeamMember.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setTeamMemberRole("Account Manager");

    // Act
    String toString = accountTeamMember.toString();

    // Assert
    assertNotNull(toString);
    assertTrue(toString.contains("AccountTeamMember"));
    assertTrue(toString.contains("001XXXXXXXXXXXXXXX"));
    assertTrue(toString.contains("005XXXXXXXXXXXXXXX"));
    assertTrue(toString.contains("Account Manager"));
  }

  @Test
  void testAccountTeamMemberConstructors() {
    // Test no-args constructor
    AccountTeamMember accountTeamMember1 = new AccountTeamMember();
    assertNotNull(accountTeamMember1);
    assertNull(accountTeamMember1.getId());

    // Test all-args constructor
    AccountTeamMember accountTeamMember2 =
        new AccountTeamMember(
            "001XXXXXXXXXXXXXXX",
            "005XXXXXXXXXXXXXXX",
            "001XXXXXXXXXXXXXXX",
            "Account Manager",
            "Edit",
            "Edit",
            "Edit",
            "Edit");
    assertNotNull(accountTeamMember2);
    assertEquals("001XXXXXXXXXXXXXXX", accountTeamMember2.getId());
    assertEquals("005XXXXXXXXXXXXXXX", accountTeamMember2.getUserId());
    assertEquals("001XXXXXXXXXXXXXXX", accountTeamMember2.getAccountId());
    assertEquals("Account Manager", accountTeamMember2.getTeamMemberRole());
    assertEquals("Edit", accountTeamMember2.getAccountAccessLevel());
    assertEquals("Edit", accountTeamMember2.getOpportunityAccessLevel());
    assertEquals("Edit", accountTeamMember2.getCaseAccessLevel());
    assertEquals("Edit", accountTeamMember2.getContactAccessLevel());
  }

  @Test
  void testAccountTeamMemberIgnoreUnknownProperties() throws JsonProcessingException {
    // Arrange
    String jsonWithUnknownProperty =
        """
        {
          "Id": "001XXXXXXXXXXXXXXX",
          "UserId": "005XXXXXXXXXXXXXXX",
          "UnknownProperty": "SomeValue",
          "TeamMemberRole": "Account Manager"
        }
        """;

    ObjectMapper objectMapper = new ObjectMapper();

    // Act & Assert - Should not throw exception due to @JsonIgnoreProperties(ignoreUnknown = true)
    assertDoesNotThrow(
        () -> {
          AccountTeamMember accountTeamMember =
              objectMapper.readValue(jsonWithUnknownProperty, AccountTeamMember.class);
          assertEquals("001XXXXXXXXXXXXXXX", accountTeamMember.getId());
          assertEquals("005XXXXXXXXXXXXXXX", accountTeamMember.getUserId());
          assertEquals("Account Manager", accountTeamMember.getTeamMemberRole());
        });
  }
}
