/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import com.app.cargill.crescendo.service.ICrescendoAccountService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoAccountControllerTest {

  @Mock private ICrescendoAccountService crescendoAccountService;
  @Mock private ResourceBundleMessageSource bundleMessageSource;
  @InjectMocks private SalesforceCrescendoAccountController controller;

  private AccountDocument testAccountDocument;

  @BeforeEach
  void setUp() {
    testAccountDocument = createTestAccountDocument();
  }

  @Test
  void createAccount_Success() throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    String expectedAccountId = "001XXXXXXXXXXXXXXX";
    when(crescendoAccountService.createAccount(
            eq(testAccountDocument), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedAccountId);

    // Act
    String result = controller.createAccount(testAccountDocument);

    // Assert
    assertNotNull(result);
    assertEquals(expectedAccountId, result);
  }

  @Test
  void createAccount_ThrowsJsonProcessingException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoAccountService.createAccount(
            eq(testAccountDocument), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(new JsonProcessingException("JSON processing error") {});

    // Act & Assert
    assertThrows(
        JsonProcessingException.class, () -> controller.createAccount(testAccountDocument));
  }

  @Test
  void createAccount_ThrowsCustomDEExceptions() throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoAccountService.createAccount(
            eq(testAccountDocument), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(new CustomDEExceptions("Account creation failed", HttpStatus.SC_BAD_REQUEST));

    // Act & Assert
    assertThrows(CustomDEExceptions.class, () -> controller.createAccount(testAccountDocument));
  }

  @Test
  void updateAccount_Success() throws CustomDEExceptions {
    // Arrange
    String expectedResult = "Account updated successfully";
    when(crescendoAccountService.updateAccount(
            eq(testAccountDocument), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedResult);

    // Act
    String result = controller.updateAccount(testAccountDocument);

    // Assert
    assertNotNull(result);
    assertEquals(expectedResult, result);
  }

  @Test
  void updateAccount_ThrowsCustomDEExceptions() throws CustomDEExceptions {
    // Arrange
    when(crescendoAccountService.updateAccount(
            eq(testAccountDocument), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(new CustomDEExceptions("Account update failed", HttpStatus.SC_BAD_REQUEST));

    // Act & Assert
    assertThrows(CustomDEExceptions.class, () -> controller.updateAccount(testAccountDocument));
  }

  @Test
  void createAccount_WithNullAccountDocument() throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoAccountService.createAccount(
            eq(null), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(
            new CustomDEExceptions("Account document cannot be null", HttpStatus.SC_BAD_REQUEST));

    // Act & Assert
    assertThrows(CustomDEExceptions.class, () -> controller.createAccount(null));
  }

  @Test
  void updateAccount_WithNullAccountDocument() throws CustomDEExceptions {
    // Arrange
    when(crescendoAccountService.updateAccount(
            eq(null), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(
            new CustomDEExceptions("Account document cannot be null", HttpStatus.SC_BAD_REQUEST));

    // Act & Assert
    assertThrows(CustomDEExceptions.class, () -> controller.updateAccount(null));
  }

  private AccountDocument createTestAccountDocument() {
    return AccountDocument.builder()
        .id(UUID.randomUUID())
        .accountName("Test Account")
        .accountType(1)
        .ownerId("<EMAIL>")
        .type("012XXXXXXXXXXXXXXX")
        .physicalAddress(
            Address.builder()
                .city("Test City")
                .country("USA")
                .postalCode("12345")
                .stateOrProvince("Test State")
                .build())
        .customerCode("CUST001")
        .segmentStepOneId("Noah")
        .nineBoxStepTwoID("1 - Large/Extensive")
        .build();
  }
}
