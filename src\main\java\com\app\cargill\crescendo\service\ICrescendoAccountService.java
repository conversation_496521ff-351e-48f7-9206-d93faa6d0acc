/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service;

import com.app.cargill.document.AccountDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;

public interface ICrescendoAccountService {

  String createAccount(
      AccountDocument accountDocument, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException;

  String updateAccount(
      AccountDocument accountDocument,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions;
}
