/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;

class CrescendoActivityServiceImplTest {

  @InjectMocks private CrescendoActivityServiceImpl crescendoActivityService;

  @Mock private CrescendoApiServiceImpl apiServiceImpl;

  @Mock private ResourceBundleMessageSource bundleMessageSource;

  private EventsCrescendo eventsCrescendo;
  private Locale locale;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    eventsCrescendo = new EventsCrescendo();
    eventsCrescendo.setEventId("12345");
    eventsCrescendo.setWhatId("67890");
    eventsCrescendo.setSubject("Test Subject");
    eventsCrescendo.setStartDateTime("2025-07-23T10:00:00");
    eventsCrescendo.setEndDateTime("2025-07-23T11:00:00");
    eventsCrescendo.setOwnerId("owner123");
    eventsCrescendo.setExternalId("external123");
    eventsCrescendo.setReportLink("http://example.com/report");
    eventsCrescendo.setMobileFirst(true);
    eventsCrescendo.setExternalAccountId("account123");

    locale = Locale.ENGLISH;
  }

  @Test
  void testCreateActivity() throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = mock(AccessTokenAndApiPathDto.class);
    AuthToken authToken = mock(AuthToken.class);
    CreateRecordResponse recordResponse = mock(CreateRecordResponse.class);

    when(apiServiceImpl.getTokenAndApiPath()).thenReturn(token);
    when(token.getApiPath()).thenReturn("http://api.example.com");
    when(token.getAuthToken()).thenReturn(authToken);
    when(apiServiceImpl.createRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event")))
        .thenReturn(recordResponse);
    when(recordResponse.getId()).thenReturn("12345");

    String result =
        crescendoActivityService.createActivity(eventsCrescendo, locale, bundleMessageSource);

    assertEquals("12345", result);
    verify(apiServiceImpl)
        .createRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event"));
    verify(apiServiceImpl).getTokenAndApiPath();
  }

  @Test
  void testCreateActivityWithNullResponse() throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = mock(AccessTokenAndApiPathDto.class);
    AuthToken authToken = mock(AuthToken.class);

    when(apiServiceImpl.getTokenAndApiPath()).thenReturn(token);
    when(token.getApiPath()).thenReturn("http://api.example.com");
    when(token.getAuthToken()).thenReturn(authToken);
    when(apiServiceImpl.createRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event")))
        .thenReturn(null);

    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoActivityService.createActivity(
                    eventsCrescendo, locale, bundleMessageSource));

    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(apiServiceImpl).getTokenAndApiPath();
  }

  @Test
  void testCreateActivityWithJsonProcessingException()
      throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = mock(AccessTokenAndApiPathDto.class);
    AuthToken authToken = mock(AuthToken.class);

    when(apiServiceImpl.getTokenAndApiPath()).thenReturn(token);
    when(token.getApiPath()).thenReturn("http://api.example.com");
    when(token.getAuthToken()).thenReturn(authToken);
    when(apiServiceImpl.createRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event")))
        .thenThrow(new JsonProcessingException("Error processing JSON") {});

    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoActivityService.createActivity(
                    eventsCrescendo, locale, bundleMessageSource));

    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(apiServiceImpl).getTokenAndApiPath();
  }

  @Test
  void testUpdateActivity() throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = mock(AccessTokenAndApiPathDto.class);
    AuthToken authToken = mock(AuthToken.class);

    when(apiServiceImpl.getTokenAndApiPath()).thenReturn(token);
    when(token.getApiPath()).thenReturn("http://api.example.com");
    when(token.getAuthToken()).thenReturn(authToken);

    String result =
        crescendoActivityService.updateActivity(eventsCrescendo, locale, bundleMessageSource);

    assertEquals("12345", result);
    verify(apiServiceImpl)
        .updateRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event/00UVD000007BRle2AG"));
    verify(apiServiceImpl).getTokenAndApiPath();
  }

  @Test
  void testUpdateActivityWithJsonProcessingException()
      throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = mock(AccessTokenAndApiPathDto.class);
    AuthToken authToken = mock(AuthToken.class);

    when(apiServiceImpl.getTokenAndApiPath()).thenReturn(token);
    when(token.getApiPath()).thenReturn("http://api.example.com");
    when(token.getAuthToken()).thenReturn(authToken);

    doAnswer(
            invocation -> {
              throw new JsonProcessingException("Error processing JSON") {};
            })
        .when(apiServiceImpl)
        .updateRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event/00UVD000007BRle2AG"));

    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoActivityService.updateActivity(
                    eventsCrescendo, locale, bundleMessageSource));

    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(apiServiceImpl).getTokenAndApiPath();
    verify(apiServiceImpl)
        .updateRecord(
            eq(authToken),
            eq(eventsCrescendo),
            any(ParameterizedTypeReference.class),
            eq("http://api.example.com/sobjects/Event/00UVD000007BRle2AG"));
  }

  @Test
  void testCreateActivityWithTokenError() throws CustomDEExceptions {
    doAnswer(
            invocation -> {
              throw new CustomDEExceptions("Token error", HttpStatus.SC_FORBIDDEN);
            })
        .when(apiServiceImpl)
        .getTokenAndApiPath();

    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoActivityService.createActivity(eventsCrescendo, locale, bundleMessageSource);
            });

    assertEquals("Token error", exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(apiServiceImpl).getTokenAndApiPath();
  }

  @Test
  void testUpdateActivityWithTokenError() throws CustomDEExceptions {
    doAnswer(
            invocation -> {
              throw new CustomDEExceptions("Token error", HttpStatus.SC_FORBIDDEN);
            })
        .when(apiServiceImpl)
        .getTokenAndApiPath();

    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> {
              crescendoActivityService.updateActivity(eventsCrescendo, locale, bundleMessageSource);
            });

    assertEquals("Token error", exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(apiServiceImpl).getTokenAndApiPath();
  }
}
