/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.repository.UserJourneyRepository;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.atomic.AtomicInteger;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;

@ExtendWith(MockitoExtension.class)
class UserJourneyExcelReportServiceImplTest {

  @Mock private UserJourneyServiceImpl userJourneyService;

  @Mock private UserJourneyRepository journeyRepository;

  @InjectMocks private UserJourneyExcelReportServiceImpl reportService;

  private ResourceBundleMessageSource messageSource;
  private Locale locale;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    messageSource = new ResourceBundleMessageSource();
    messageSource.setBasename("messages");
    locale = Locale.US;
  }

  @Test
  void testGetFileName_shouldReturnCorrectFormat() {
    String fileName = reportService.getFileName(Instant.now());
    assertTrue(fileName.startsWith("UserJourneyAnalytics_"));
    assertTrue(fileName.endsWith(".xlsx"));
  }

  @Test
  void testPrepareExportToExcel_shouldReturnValidExcelFile() throws IOException {
    Instant testDate = Instant.now();

    ByteArrayResource resource =
        reportService.prepareExportToExcel(testDate, messageSource, locale);
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);

    try (InputStream is = resource.getInputStream()) {
      assertDoesNotThrow(() -> WorkbookFactory.create(is));
    }
  }

  @Test
  void testPrepareExportToExcel_shouldHandleEmptyList() throws IOException {
    Instant testDate = Instant.now();

    ByteArrayResource resource =
        reportService.prepareExportToExcel(testDate, messageSource, locale);
    assertNotNull(resource);
    assertTrue(resource.contentLength() > 0);
  }

  @Test
  void testPrepareExportToImage_shouldThrowUnsupportedException() {
    assertThrows(
        UnsupportedOperationException.class,
        () -> reportService.prepareExportToImage(new Object(), messageSource, locale));
  }

  @Test
  void testCreateDataRows_addsCorrectRowsToSheet() {
    try (XSSFWorkbook workbook = new XSSFWorkbook()) {
      XSSFSheet sheet = workbook.createSheet("Test Sheet");
      XSSFCellStyle dummyStyle = workbook.createCellStyle();

      AtomicInteger rowCounter = new AtomicInteger(0);

      UserJourneyPathDto pathDto =
          UserJourneyPathDto.builder()
              .eventName("Login")
              .path("/home")
              .eventTriggerStartTime("2024-06-01T10:00:00Z")
              .eventTriggerEndTime("2024-06-01T10:05:00Z")
              .eventDuration("5 mins")
              .build();

      UserJourneyDto journeyDto =
          UserJourneyDto.builder()
              .userEmail("<EMAIL>")
              .userCountry("Pakistan")
              .paths(List.of(pathDto))
              .build();

      reportService.createDataRows(sheet, dummyStyle, List.of(journeyDto), rowCounter);

      // Assert row count
      assertEquals(1, sheet.getPhysicalNumberOfRows());

      // Optional: Assert cell content
      Row firstRow = sheet.getRow(0);
      assertEquals("<EMAIL>", firstRow.getCell(0).getStringCellValue());
      assertEquals("Pakistan", firstRow.getCell(1).getStringCellValue());
      assertEquals("Login", firstRow.getCell(2).getStringCellValue());
      assertEquals("/home", firstRow.getCell(3).getStringCellValue());
      assertEquals("2024-06-01T10:00:00Z", firstRow.getCell(4).getStringCellValue());
      assertEquals("2024-06-01T10:05:00Z", firstRow.getCell(5).getStringCellValue());
      assertEquals("5 mins", firstRow.getCell(6).getStringCellValue());
    } catch (IOException e) {
      e.printStackTrace();
    }
  }
}
