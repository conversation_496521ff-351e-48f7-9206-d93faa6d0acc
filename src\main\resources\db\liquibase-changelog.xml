<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

  <!--NOSONAR <include file="changelog/1-init-schema.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/2-init-sample-data.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/3-segments-data.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/4-segments-data-update.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/5-segments-data-update-revert.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/6-segments-data-update-revert.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/7-segments-data-update-revert.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/8-added-default-value-in-segments.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/10-tasks-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/11-tasks-table-meta.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/12-content-details-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/13-notes-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/14-milk-processors-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/15-activities-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/16-add-role-integration.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/17-add-configurations.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/18-update-configurations.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/19-create-user-migration-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/20-Countrytools-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/21-Country-tools-India.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/22-country-tools-forage-penn-state.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/23-indexes-log.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/24-update-date-trigger.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/25-users-principal-name-length.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/26-pens-correct-index-column.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/27-visits-indexes.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/28-countrytools-india-calfheifer.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/29-site-mappings-site-id-unique-index.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/30-update-state-distrito-federal-name.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/31-analytics-table.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/32-trigger-update.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/33-tools-updated-india.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/34-profitabilityanalysis-countrytools.xml" relativeToChangelogFile="true"/> -->
  <!--NOSONAR <include file="changelog/35-country-tools-mexico.xml" relativeToChangelogFile="true"/> -->
   <include file="changelog/36-return-over-feed-pricing.xml" relativeToChangelogFile="true"/> 
   <include file="changelog/37-return-over-feed-milk-pricing.xml" relativeToChangelogFile="true"/> 
   <include file="changelog/38-return-over-feed-country-tools.xml" relativeToChangelogFile="true"/> 
   <include file="changelog/39-return-over-feed-pricing-name-update.xml" relativeToChangelogFile="true"/> 
   <include file="changelog/40-return-over-feed-pricing-milk-names.xml" relativeToChangelogFile="true"/>
   <include file="changelog/41-default-app-settings-active-countries-insert.xml" relativeToChangelogFile="true"/>
   <include file="changelog/42-return-over-feed-hl-value-insert.xml" relativeToChangelogFile="true"/>
   <include file="changelog/43-country-tools-Neolait.xml" relativeToChangelogFile="true"/>
  <include file="changelog/44-country-tools-Provimi.xml" relativeToChangelogFile="true"/>



</databaseChangeLog>
