/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReturnOverFeedPerKgFatDataPoints {

  private Double rofPerKgButterFat;
  private Double concentrateCostPerKgButterFat;
  private Double totalRevenueDollarKgPerFat;
  private Double totalCostsPerKgPerFat;
  private String visitDate;
}
