/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EventsCrescendo implements Serializable {
  private static final long serialVersionUID = 1L;

  @JsonProperty("EventId")
  @JsonIgnore
  public String eventId;

  @JsonProperty("WhatId")
  private String whatId;

  @JsonProperty("Subject")
  private String subject;

  @JsonProperty("StartDateTime")
  private String startDateTime;

  @JsonProperty("EndDateTime")
  private String endDateTime;

  @JsonProperty("OwnerId")
  private String ownerId;

  @JsonProperty("External_Id__c")
  private String externalId;

  @JsonProperty("Report_Link__c")
  private String reportLink;

  @JsonProperty("Mobile_First__c")
  private Boolean mobileFirst;

  @JsonProperty("External_Account_Id__c")
  private String externalAccountId;
}
