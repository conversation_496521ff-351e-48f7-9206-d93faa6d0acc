<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="043" author="Nivetha">
	 <sql splitStatements="false">

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'fc525f93-97c4-408c-b65b-fc9f66750f29', '2025-07-21 11:39:46.023', '{"id": "fc525f93-97c4-408c-b65b-fc9f66750f29", "IsNew": false, "ToolId": "HeatStress", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'e5151661-ddf8-41b9-a1ff-312bc5b02ef9', '2025-07-21 11:39:46.023', '{"id": "e5151661-ddf8-41b9-a1ff-312bc5b02ef9", "IsNew": false, "ToolId": "PenTimeBudgetTool", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '848cd781-a8bf-435b-a133-25a117aa5fec', '2025-07-21 11:39:46.023', '{"id": "848cd781-a8bf-435b-a133-25a117aa5fec", "IsNew": false, "ToolId": "RumenHealth", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '99673485-be62-4eef-b0cc-8e08351dbe57', '2025-07-21 11:39:46.023', '{"id": "99673485-be62-4eef-b0cc-8e08351dbe57", "IsNew": false, "ToolId": "RumenHealthManureScore", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '5517b407-ca0e-4ad7-acb2-f2849cfc013c', '2025-07-21 11:39:46.023', '{"id": "5517b407-ca0e-4ad7-acb2-f2849cfc013c", "IsNew": false, "ToolId": "ManureScreener", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '8df46d7d-40a8-4ce3-b5e6-db135bbd7fef', '2025-07-21 11:39:46.023', '{"id": "8df46d7d-40a8-4ce3-b5e6-db135bbd7fef", "IsNew": false, "ToolId": "RumenFill", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'f8ced762-08fa-48c2-8bee-57e9b6d6849a', '2025-07-21 11:39:46.023', '{"id": "f8ced762-08fa-48c2-8bee-57e9b6d6849a", "IsNew": false, "ToolId": "LocomotionScore", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'c74d9916-bf35-4c6e-a68d-47ebed5b32c4', '2025-07-21 11:39:46.023', '{"id": "c74d9916-bf35-4c6e-a68d-47ebed5b32c4", "IsNew": false, "ToolId": "BodyCondition", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '2f82d88b-eb68-414e-be58-285f067fb01f', '2025-07-21 11:39:46.023', '{"id": "2f82d88b-eb68-414e-be58-285f067fb01f", "IsNew": false, "ToolId": "MetabolicIncidence", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '272326f2-873f-4916-9f2d-b945c2f788c1', '2025-07-21 11:39:46.023', '{"id": "272326f2-873f-4916-9f2d-b945c2f788c1", "IsNew": false, "ToolId": "ForageAuditScorecard", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '3479125f-7744-40ca-a388-86dcb78b6dd6', '2025-07-21 11:39:46.023', '{"id": "3479125f-7744-40ca-a388-86dcb78b6dd6", "IsNew": false, "ToolId": "PileAndBunker", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '1e8c68cb-1438-4ac8-9fbe-2b2edbd55096', '2025-07-21 11:39:46.023', '{"id": "1e8c68cb-1438-4ac8-9fbe-2b2edbd55096", "IsNew": false, "ToolId": "MilkSoldEvaluation", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '6612127d-246e-4d71-b67b-74fd9b587abd', '2025-07-21 11:39:46.023', '{"id": "6612127d-246e-4d71-b67b-74fd9b587abd", "IsNew": false, "ToolId": "RoboticMilkEvaluation", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '3a8daa3d-479a-4632-8656-add29df818d5', '2025-07-21 11:39:46.023', '{"id": "3a8daa3d-479a-4632-8656-add29df818d5", "IsNew": false, "ToolId": "Revenue", "CountryId": "Neolait_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '7a504b99-d76d-492f-8f88-11f12f3cd35c', '2025-07-21 11:39:46.023', '{"id": "7a504b99-d76d-492f-8f88-11f12f3cd35c", "IsNew": false, "ToolId": "UrinePHTool", "CountryId": "Neolait_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '74e886ad-9f7a-4e60-9f1f-e258ec797837', '2025-07-21 11:39:46.023', '{"id": "74e886ad-9f7a-4e60-9f1f-e258ec797837", "IsNew": false, "ToolId": "ReadyToMilk", "CountryId": "Neolait_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'ed091764-ef11-4a97-9e35-46b712be5290', '2025-07-21 11:39:46.023', '{"id": "ed091764-ef11-4a97-9e35-46b712be5290", "IsNew": false, "ToolId": "CalfHeiferScorecard", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "CalfandHeifer", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'e17748bc-20a6-4354-a968-3d45b1e3b80a', '2025-07-21 11:39:46.023', '{"id": "e17748bc-20a6-4354-a968-3d45b1e3b80a", "IsNew": false, "ToolId": "TMRParticleScore", "CountryId": "Neolait_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

</sql>


	</changeSet>

</databaseChangeLog>