/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.document.Contact;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.util.Locale;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoContactControllerTest {

  @Mock private ICrescendoContactService crescendoContactServiceImpl;
  @Mock private ResourceBundleMessageSource bundleMessageSource;
  @InjectMocks private SalesforceCrescendoContactController controller;

  private Contact testContact;

  @BeforeEach
  void setUp() {
    testContact = createTestContact();
  }

  @Test
  void createContact_Success() throws CustomDEExceptions {
    // Arrange
    String expectedId = "003XXXXXXXXXXXXXXX";
    when(crescendoContactServiceImpl.createContact(
            any(Contact.class), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createContact(testContact);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoContactServiceImpl)
        .createContact(eq(testContact), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createContact_ServiceThrowsException() throws CustomDEExceptions {
    // Arrange
    String errorMessage = "Failed to create contact";
    when(crescendoContactServiceImpl.createContact(
            any(Contact.class), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenThrow(new CustomDEExceptions(errorMessage, HttpStatus.SC_FORBIDDEN));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(CustomDEExceptions.class, () -> controller.createContact(testContact));

    assertEquals(errorMessage, exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(crescendoContactServiceImpl)
        .createContact(eq(testContact), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  @Test
  void createContact_WithNullContact() throws CustomDEExceptions {
    // Arrange
    String expectedId = "003XXXXXXXXXXXXXXX";
    when(crescendoContactServiceImpl.createContact(
            any(), eq(Locale.ENGLISH), eq(bundleMessageSource)))
        .thenReturn(expectedId);

    // Act
    String result = controller.createContact(null);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoContactServiceImpl)
        .createContact(eq(null), eq(Locale.ENGLISH), eq(bundleMessageSource));
  }

  private Contact createTestContact() {
    return Contact.builder()
        .contactId(UUID.randomUUID())
        .goldenRecordAcountId("001XXXXXXXXXXXXXXX")
        .sFDCContactId("003XXXXXXXXXXXXXXX")
        .firstName("John")
        .lastName("Doe")
        .phoneNumber("************")
        .build();
  }
}
