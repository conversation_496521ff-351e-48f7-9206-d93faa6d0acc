/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.document.Contact;
import com.app.cargill.exceptions.CustomDEExceptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/contact")
@Tag(
    name = "Salesforce Crescendo Contact",
    description = "Controller related to actions over Contact objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoContactController {

  private final ICrescendoContactService crescendoContactServiceImpl;
  private final ResourceBundleMessageSource bundleMessageSource;

  @PostMapping("/create")
  @Operation(
      summary = "Create a Contact directly from APP to CRESCENDO",
      description = "This api will create a Contact Directly from APP to Crescendo")
  public String createContact(@RequestBody Contact contact) throws CustomDEExceptions {
    return crescendoContactServiceImpl.createContact(contact, Locale.ENGLISH, bundleMessageSource);
  }

  @PutMapping("/update")
  @Operation(
      summary = "Update a Contact directly from APP to CRESCENDO",
      description = "This api will Update a Contact Directly from APP to Crescendo")
  public String updateContact(@RequestBody Contact contact) throws CustomDEExceptions {
    return crescendoContactServiceImpl.updateContact(contact, Locale.ENGLISH, bundleMessageSource);
  }
}
