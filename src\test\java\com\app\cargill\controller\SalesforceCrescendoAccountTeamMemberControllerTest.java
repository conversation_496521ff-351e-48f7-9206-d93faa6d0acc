/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.AccountTeamMember;
import com.app.cargill.crescendo.service.ICrescendoAccountTeamMemberService;
import com.app.cargill.exceptions.CustomDEExceptions;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SalesforceCrescendoAccountTeamMemberControllerTest {

  @Mock private ICrescendoAccountTeamMemberService crescendoAccountTeamMemberService;

  @InjectMocks private SalesforceCrescendoAccountTeamMemberController controller;

  private AccountTeamMember testAccountTeamMember;

  @BeforeEach
  void setUp() {
    testAccountTeamMember = createTestAccountTeamMember();
  }

  @Test
  void createAccountTeamMember_Success() throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoAccountTeamMemberService.createAccountTeamMember(
            any(AccountTeamMember.class), isNull(), isNull()))
        .thenReturn(expectedId);

    // Act
    String result = controller.createAccountTeamMember(testAccountTeamMember);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoAccountTeamMemberService)
        .createAccountTeamMember(eq(testAccountTeamMember), isNull(), isNull());
  }

  @Test
  void createAccountTeamMember_ServiceThrowsException() throws CustomDEExceptions {
    // Arrange
    String errorMessage = "Failed to create account team member";
    when(crescendoAccountTeamMemberService.createAccountTeamMember(
            any(AccountTeamMember.class), isNull(), isNull()))
        .thenThrow(new CustomDEExceptions(errorMessage, HttpStatus.SC_FORBIDDEN));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> controller.createAccountTeamMember(testAccountTeamMember));

    assertEquals(errorMessage, exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(crescendoAccountTeamMemberService)
        .createAccountTeamMember(eq(testAccountTeamMember), isNull(), isNull());
  }

  @Test
  void createAccountTeamMember_WithNullInput() throws CustomDEExceptions {
    // Arrange
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoAccountTeamMemberService.createAccountTeamMember(any(), isNull(), isNull()))
        .thenReturn(expectedId);

    // Act
    String result = controller.createAccountTeamMember(null);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoAccountTeamMemberService).createAccountTeamMember(isNull(), isNull(), isNull());
  }

  @Test
  void createAccountTeamMember_WithEmptyAccountTeamMember() throws CustomDEExceptions {
    // Arrange
    AccountTeamMember emptyAccountTeamMember = new AccountTeamMember();
    String expectedId = "001XXXXXXXXXXXXXXX";
    when(crescendoAccountTeamMemberService.createAccountTeamMember(
            any(AccountTeamMember.class), isNull(), isNull()))
        .thenReturn(expectedId);

    // Act
    String result = controller.createAccountTeamMember(emptyAccountTeamMember);

    // Assert
    assertNotNull(result);
    assertEquals(expectedId, result);
    verify(crescendoAccountTeamMemberService)
        .createAccountTeamMember(eq(emptyAccountTeamMember), isNull(), isNull());
  }

  private AccountTeamMember createTestAccountTeamMember() {
    AccountTeamMember accountTeamMember = new AccountTeamMember();
    accountTeamMember.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setTeamMemberRole("Account Manager");
    accountTeamMember.setAccountAccessLevel("Edit");
    accountTeamMember.setOpportunityAccessLevel("Edit");
    accountTeamMember.setCaseAccessLevel("Edit");
    accountTeamMember.setContactAccessLevel("Edit");
    return accountTeamMember;
  }
}
