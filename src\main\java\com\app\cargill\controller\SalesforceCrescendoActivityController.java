/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.crescendo.service.ICrescendoActivityService;
import com.app.cargill.exceptions.CustomDEExceptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/activity")
@Tag(
    name = "Salesforce Crescendo Activity",
    description = "Controller related to actions over Activity objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoActivityController {

  private final ICrescendoActivityService crescendoActivityService;
  private final ResourceBundleMessageSource bundleMessageSource;

  @PostMapping("/create")
  @Operation(
      summary = "Create an Activity directly from APP to CRESCENDO",
      description = "This api will create an Activity Directly from APP to Crescendo")
  public String createActivity(@RequestBody EventsCrescendo eventsCrescendo)
      throws CustomDEExceptions {
    return crescendoActivityService.createActivity(
        eventsCrescendo, Locale.ENGLISH, bundleMessageSource);
  }

  @PatchMapping("/update")
  @Operation(
      summary = "Update an Activity directly from APP to CRESCENDO",
      description = "This api will update an Activity Directly from APP to Crescendo")
  public String updateActivity(@RequestBody EventsCrescendo eventsCrescendo)
      throws CustomDEExceptions {
    return crescendoActivityService.updateActivity(
        eventsCrescendo, Locale.ENGLISH, bundleMessageSource);
  }
}
