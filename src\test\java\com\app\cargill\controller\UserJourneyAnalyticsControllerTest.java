/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.service.IUserJourneyService;
import com.app.cargill.service.impl.UserJourneyExcelReportServiceImpl;
import java.time.Instant;
import java.util.Arrays;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class UserJourneyAnalyticsControllerTest {

  @Mock private IUserJourneyService userJourneyService;

  @Mock private UserJourneyExcelReportServiceImpl userJourneyExcelReportService;

  @Mock private ResourceBundleMessageSource resourceBundleMessageSource;

  @InjectMocks private UserJourneyAnalyticsController controller;

  private UserJourneyRequestDto testRequestDto;
  private UserJourneyDto testResponseDto;

  @BeforeEach
  void setUp() {
    // Create test data
    UserJourneyPathDto pathDto1 =
        UserJourneyPathDto.builder()
            .eventName("DASHBOARD_NOTES")
            .path("dashboard/notes")
            .eventTriggerStartTime("12-15-20")
            .eventTriggerEndTime("24-24-24")
            .eventDuration("3 seconds")
            .build();

    UserJourneyPathDto pathDto2 =
        UserJourneyPathDto.builder()
            .eventName("CUSTOMER_SITES_NOTES")
            .path("dashboard/customers/sites/notes")
            .eventTriggerStartTime("12-15-20")
            .eventTriggerEndTime("24-24-24")
            .eventDuration("3 seconds")
            .build();

    testRequestDto =
        UserJourneyRequestDto.builder()
            .userEmail("<EMAIL>")
            .userCountry("test")
            .paths(Arrays.asList(pathDto1, pathDto2))
            .build();

    testResponseDto =
        UserJourneyDto.builder()
            .id(UUID.randomUUID())
            .userEmail("<EMAIL>")
            .userCountry("test")
            .paths(Arrays.asList(pathDto1, pathDto2))
            .createdDate(Instant.now())
            .updatedDate(Instant.now())
            .deleted(false)
            .build();
  }

  @Test
  void testSaveUserJourney_Success() {
    // Given
    when(userJourneyService.save(any(UserJourneyRequestDto.class))).thenReturn(testResponseDto);

    // When
    ResponseEntity<?> response = controller.saveUserJourney(testRequestDto);

    // Then
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
  }

  @Test
  void testExportUserJourneyToExcel_Success() throws Exception {
    // Given
    Instant lastUpdatedDate = Instant.now().minusSeconds(3600); // 1 hour ago
    ByteArrayResource mockResource = new ByteArrayResource("test excel content".getBytes());

    when(userJourneyExcelReportService.prepareExportToExcel(
            any(Instant.class), any(ResourceBundleMessageSource.class), any()))
        .thenReturn(mockResource);

    // When
    ResponseEntity<ByteArrayResource> response =
        controller.exportUserJourneyToExcel(lastUpdatedDate, "EN");

    // Then
    assertEquals(HttpStatus.OK, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals(mockResource, response.getBody());
  }
}
