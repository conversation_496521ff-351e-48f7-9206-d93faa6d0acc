/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.integration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.controller.SalesforceCrescendoApplicationMappingController;
import com.app.cargill.crescendo.service.ICrescendoApplicationMappingService;
import com.app.cargill.crescendo.service.impl.CrescendoApiServiceImpl;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@SpringBootTest(
    classes = {
      SalesforceCrescendoApplicationMappingController.class,
      com.app.cargill.crescendo.service.impl.CrescendoApplicationMappingServiceImpl.class
    })
class CrescendoApplicationMappingIntegrationTest {

  @MockBean private CrescendoApiServiceImpl crescendoApiService;
  @MockBean private ResourceBundleMessageSource bundleMessageSource;

  @Autowired private SalesforceCrescendoApplicationMappingController controller;
  @Autowired private ICrescendoApplicationMappingService service;

  private SiteMappingDocument testSiteMapping;
  private AuthToken testAuthToken;
  private AccessTokenAndApiPathDto testTokenAndApiPath;
  private CreateRecordResponse testCreateRecordResponse;

  @BeforeEach
  void setUp() {
    testSiteMapping = createTestSiteMappingDocument();
    testAuthToken = createTestAuthToken();
    testTokenAndApiPath = createTestAccessTokenAndApiPathDto();
    testCreateRecordResponse = createTestCreateRecordResponse();
  }

  @Test
  void integrationTest_ControllerToService_Success()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class), any(), any(ParameterizedTypeReference.class), anyString()))
        .thenReturn(testCreateRecordResponse);

    // Act
    String result = controller.createApplicationMapping(testSiteMapping);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);

    // Verify the service was called correctly
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService)
        .createRecord(
            eq(testAuthToken),
            any(),
            any(ParameterizedTypeReference.class),
            eq("/services/data/v58.0/sobjects/Application_Mapping__c"));
  }

  @Test
  void integrationTest_ControllerToService_ServiceThrowsException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class), any(), any(ParameterizedTypeReference.class), anyString()))
        .thenThrow(new RuntimeException("Salesforce API error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class, () -> controller.createApplicationMapping(testSiteMapping));

    assertNotNull(exception);
    assertEquals("Salesforce API error", exception.getMessage());

    // Verify the service was called
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService)
        .createRecord(
            any(AuthToken.class), any(), any(ParameterizedTypeReference.class), anyString());
  }

  @Test
  void integrationTest_EndToEndDataFlow() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class), any(), any(ParameterizedTypeReference.class), anyString()))
        .thenReturn(testCreateRecordResponse);

    // Act
    String result = controller.createApplicationMapping(testSiteMapping);

    // Assert - Verify the complete data flow
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);

    // Verify that the correct URL was constructed
    verify(crescendoApiService)
        .createRecord(
            eq(testAuthToken),
            any(),
            any(ParameterizedTypeReference.class),
            eq("/services/data/v58.0/sobjects/Application_Mapping__c"));
  }

  private SiteMappingDocument createTestSiteMappingDocument() {
    return SiteMappingDocument.builder()
        .id(UUID.fromString("8d591af8-0ab2-41b6-9a8c-7ed4f70a389d"))
        .labyrinthSiteId(UUID.fromString("1b04305c-7f6a-493e-a581-962cd8e36dee"))
        .ddwHerdId("10665")
        .maxSiteId(UUID.fromString("b7a69930-387e-40c4-a46b-4a69f6933663"))
        .labyrinthAccountId(UUID.fromString("ea9b39f0-f355-4f2d-b788-57fa96a01cfb"))
        .milkProcessorId(null)
        .dcgoId(null)
        .build();
  }

  private AuthToken createTestAuthToken() {
    AuthToken authToken = new AuthToken();
    authToken.setAccessToken("test-access-token");
    authToken.setInstanceUrl("https://test.salesforce.com");
    authToken.setIssuedAt(System.currentTimeMillis());
    return authToken;
  }

  private AccessTokenAndApiPathDto createTestAccessTokenAndApiPathDto() {
    return AccessTokenAndApiPathDto.builder()
        .authToken(testAuthToken)
        .apiPath("/services/data/v58.0")
        .build();
  }

  private CreateRecordResponse createTestCreateRecordResponse() {
    CreateRecordResponse response = new CreateRecordResponse();
    response.setId("001XXXXXXXXXXXXXXX");
    response.setSuccess(true);
    return response;
  }
}
