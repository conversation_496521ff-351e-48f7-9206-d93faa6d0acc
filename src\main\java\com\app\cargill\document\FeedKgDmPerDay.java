/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeedKgDmPerDay implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("ForageKgDMPerDay")
  private Double forageKgDMPerDay;

  @JsonProperty("GrainsKgDMPerDay")
  private Double grainsKgDMPerDay;

  @JsonProperty("PurchasedBulkKgDMPerDay")
  private Double purchasedBulkKgDMPerDay;

  @JsonProperty("TotalFeedCostKgDMPerDay")
  private Double totalFeedCostKgDMPerDay;
}
