/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl.mappers.cdp;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.Currencies;
import com.app.cargill.crescendo.constants.BusinessUnitCrescendo;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.cdp.account.AccountDocumentDTO;
import com.app.cargill.utils.BusinessToBusinessUnitMapper;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CdpAccountDocumentMapper {

  private CdpAccountDocumentMapper() {}

  public static AccountDocumentDTO mapToDto(AccountDocument accountDocument) {

    if (accountDocument == null) {
      String errorMessage = "AccountDocument should not be null";
      log.error(errorMessage);
      throw new IllegalArgumentException(errorMessage);
    }

    String businessUnitCountry = getBusinessUnitCountry(accountDocument);
    Currencies accountCurrency = getAccountCurrency(accountDocument);
    return AccountDocumentDTO.builder()
        .goldenRecordId(accountDocument.getGoldenRecordId())
        .accountName(accountDocument.getAccountName())
        .legalName(accountDocument.getLegalName())
        .accountType(accountDocument.getAccountType())
        .contacts(accountDocument.getContacts())
        .users(accountDocument.getUsers())
        .ownerId(accountDocument.getOwnerId())
        .isDuplicate(accountDocument.getIsDuplicate())
        .autoValidate(accountDocument.getAutoValidate())
        .sourceSystem(accountDocument.getSourceSystem())
        .type(accountDocument.getType())
        .socialMediaAddress(accountDocument.getSocialMediaAddress())
        .webSiteAddress(accountDocument.getWebSiteAddress())
        .parentAccountID(accountDocument.getParentAccountID())
        .externalParentAccountID(accountDocument.getExternalParentAccountID())
        .buyingGroupID(accountDocument.getBuyingGroupID())
        .externalBuyingGroupID(accountDocument.getExternalBuyingGroupID())
        .subTypeID(accountDocument.getSubTypeID())
        .externalLeadSourceID(accountDocument.getExternalLeadSourceID())
        .accountStatus(accountDocument.getAccountStatus())
        .dateOfLastVisit(accountDocument.getDateOfLastVisit())
        .dateOfLastCall(accountDocument.getDateOfLastCall())
        .wonLostId(accountDocument.getWonLostId())
        .wonLostComments(accountDocument.getWonLostComments())
        .creditFlag(accountDocument.getCreditFlag())
        .priceFlag(accountDocument.getPriceFlag())
        .serviceFlag(accountDocument.getServiceFlag())
        .performanceFlag(accountDocument.getPerformanceFlag())
        .businessSolutionFlag(accountDocument.getBusinessSolutionFlag())
        .qualityFlag(accountDocument.getQualityFlag())
        .otherFlag(accountDocument.getOtherFlag())
        .businessCountry(businessUnitCountry)
        .defaultCargillPlantID(accountDocument.getDefaultCargillPlantID())
        .defaultCustServiceID(accountDocument.getDefaultCustServiceID())
        .lastModificationDate(accountDocument.getLastModificationDate())
        .active(accountDocument.getActive())
        .brandId(accountDocument.getBrandId())
        .nineBoxStepTwoID(accountDocument.getNineBoxStepTwoID())
        .segmentStepOneId(accountDocument.getSegmentStepOneId())
        .companyEmail(accountDocument.getCompanyEmail())
        .lastInvoiceDate(accountDocument.getLastInvoiceDate())
        .lastOrderDate(accountDocument.getLastOrderDate())
        .deliveryInstructions(accountDocument.getDeliveryInstructions())
        .erpPayerId(accountDocument.getErpPayerId())
        .erpShipToId(accountDocument.getErpShipToId())
        .isServicedbyCSPro(accountDocument.getIsServicedbyCSPro())
        .lastAdminUpdate(accountDocument.getLastAdminUpdate())
        .lastInvoicesInfo(accountDocument.getLastInvoicesInfo())
        .lastOrdersInfo(accountDocument.getLastOrdersInfo())
        .phone(accountDocument.getPhone())
        .reqProcessingLog(accountDocument.getReqProcessingLog())
        .liabilities(accountDocument.getLiabilities())
        .limitChangeReasonId(accountDocument.getLimitChangeReasonId())
        .marketInfluencer(accountDocument.getMarketInfluencer())
        .otherActivityProduction(accountDocument.getOtherActivityProduction())
        .personalID(accountDocument.getPersonalID())
        .previousStatus(accountDocument.getPreviousStatus())
        .reasonDescription(accountDocument.getReasonDescription())
        .securities(accountDocument.getSecurities())
        .approvalStatus(accountDocument.getApprovalStatus())
        .assets(accountDocument.getAssets())
        .volumeEstimate(accountDocument.getVolumeEstimate())
        .marginEstimate(accountDocument.getMarginEstimate())
        .physicalAddress(accountDocument.getPhysicalAddress())
        .correspondenceAddress(accountDocument.getCorrespondenceAddress())
        .additionalInfo(accountDocument.getAdditionalInfo())
        .lstOtherBU(accountDocument.getLstOtherBU())
        .currency(accountCurrency)
        .accountNumber(accountDocument.getAccountNumber())
        .availabilityOnMarket(accountDocument.getAvailabilityOnMarket())
        .changeAccountType(accountDocument.getChangeAccountType())
        .newAccountType(accountDocument.getNewAccountType())
        .consumerStatus(accountDocument.getConsumerStatus())
        .courtId(accountDocument.getCourtId())
        .customerStatus(accountDocument.getCustomerStatus())
        .erpIdLength(accountDocument.getErpIdLength())
        .erpPayerIdLength(accountDocument.getErpPayerIdLength())
        .erpShiptoIdLength(accountDocument.getErpShiptoIdLength())
        .isMobileFirst(accountDocument.getIsMobileFirst())
        .veterinaryId(accountDocument.getVeterinaryId())
        .wonLostReasonCode(accountDocument.getWonLostReasonCode())
        .subBrandId(accountDocument.getSubBrandId())
        .currentUserProfileNameandId(accountDocument.getCurrentUserProfileNameandId())
        .lastModifiedBy(accountDocument.getLastModifiedBy())
        .ownerProfileNameandId(accountDocument.getOwnerProfileNameandId())
        .description(accountDocument.getDescription())
        .salesTerritory(accountDocument.getSalesTerritory())
        .customerCode(accountDocument.getCustomerCode())
        .userRoles(accountDocument.getUserRoles())
        .id(accountDocument.getId())
        .createUser(accountDocument.getCreateUser())
        .isDeleted(accountDocument.isDeleted())
        .lastModifyUser(accountDocument.getLastModifyUser())
        .createTimeUtc(accountDocument.getCreateTimeUtc())
        .lastModifiedTimeUtc(accountDocument.getLastModifiedTimeUtc())
        .lastSyncTimeUtc(accountDocument.getLastSyncTimeUtc())
        .isNew(accountDocument.isNew())
        .build();
  }

  static String getBusinessUnitCountry(AccountDocument accountDocument) {
    Optional<String> businessUnitOptional =
        Optional.ofNullable(accountDocument)
            .map(doc -> accountDocument.getBusinessID())
            .map(Business::fromId)
            .map(BusinessToBusinessUnitMapper::toBusinessUnit)
            .map(BusinessUnitCrescendo::getValue);

    return businessUnitOptional.isPresent() ? businessUnitOptional.get() : "NOT_FOUND";
  }

  static Currencies getAccountCurrency(AccountDocument accountDocument) {
    Optional<Currencies> accountCurrencyOptional =
        Optional.ofNullable(accountDocument)
            .map(
                doc -> {
                  if ("LM".equals(doc.getSourceSystem())) {
                    doc.setAccountCurrency(Currencies.NotSet.getValue());
                  }
                  return doc.getAccountCurrency();
                })
            .map(Currencies::fromId);

    return accountCurrencyOptional.isPresent() ? accountCurrencyOptional.get() : Currencies.NotSet;
  }
}
