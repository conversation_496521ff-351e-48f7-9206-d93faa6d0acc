/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.dto.ResponseEntityDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/crescendo/users")
@Tag(
    name = "Crescendo Users Information Controller",
    description = "Crescendo Users Information Controller")
@RequiredArgsConstructor
public class SalesforceCrescendoUserController extends BaseController {

  private final ICrescendoUserService crescendoUserService;

  @GetMapping("/getUserByEmail")
  @Operation(
      summary = "This api will return True if the user email exists on crescendo",
      description = "This api will return True if the user email exists on crescendo")
  public ResponseEntity<ResponseEntityDto<SalesforceRecordsResponse<Users>>>
      getCrescendoUserByEmail(
          @RequestParam(required = true, name = "userEmail", defaultValue = "") String userEmail)
          throws CustomDEExceptions {
    return handleSuccessResponse(crescendoUserService.getByUserEmail(userEmail));
  }
}
