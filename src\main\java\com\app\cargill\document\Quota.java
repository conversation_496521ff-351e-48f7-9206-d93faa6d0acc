/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class Quota implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("KgOfQuota")
  private Double kgOfQuota;

  @JsonProperty("IncentiveDays")
  private Integer incentiveDays;

  @JsonProperty("TotalQuota")
  private Double totalQuota;

  @JsonProperty("NoOfCowsToFillQuota")
  private Integer noOfCowsToFillQuota;

  @JsonProperty("TotalFatProtein")
  private Double totalFatProtein;

  @JsonProperty("RatioSNFPerButterfat")
  private Double ratioSNFPerButterfat;

  @JsonProperty("MaxAllowed")
  private Double maxAllowed;
}
