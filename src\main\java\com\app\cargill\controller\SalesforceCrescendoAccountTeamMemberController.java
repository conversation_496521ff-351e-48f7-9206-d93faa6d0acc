/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.model.AccountTeamMember;
import com.app.cargill.crescendo.service.ICrescendoAccountTeamMemberService;
import com.app.cargill.exceptions.CustomDEExceptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/accountTeamMember")
@Tag(
    name = "Salesforce Crescendo Account Team Member",
    description = "Controller related to actions over Account Team member objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoAccountTeamMemberController {

  private final ICrescendoAccountTeamMemberService crescendoAccountTeamMemberService;

  @PostMapping("/create")
  @Operation(
      summary = "Create an Account Team Member directly from APP to CRESCENDO",
      description = "This api will create an Account Team Member Directly from APP to Crescendo")
  public String createAccountTeamMember(@RequestBody AccountTeamMember accountTeamMember)
      throws CustomDEExceptions {
    return crescendoAccountTeamMemberService.createAccountTeamMember(accountTeamMember, null, null);
  }
}
