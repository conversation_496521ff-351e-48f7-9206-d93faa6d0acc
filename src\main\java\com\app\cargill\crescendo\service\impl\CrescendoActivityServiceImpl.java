/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.crescendo.service.ICrescendoActivityService;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@SuppressWarnings(
    "java:S1172") // Added for unused variables, which will be incorporated during translations
// integration
public class CrescendoActivityServiceImpl implements ICrescendoActivityService {

  private final CrescendoApiServiceImpl apiServiceImpl;

  @Override
  public String createActivity(
      EventsCrescendo eventsCrescendo,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = apiServiceImpl.getTokenAndApiPath();
    return createActivity(
        token.getApiPath(), token.getAuthToken(), eventsCrescendo, bundleMessageSource, locale);
  }

  private String createActivity(
      String apiPath,
      AuthToken authToken,
      EventsCrescendo eventsCrescendo,
      ResourceBundleMessageSource bundleMessageSource,
      Locale locale)
      throws CustomDEExceptions {
    try {
      String eventUrl = String.format("%s/sobjects/Event", apiPath);

      log.info("CREATED CRESCENDO ACTIVITY STARTED {}", eventsCrescendo);
      CreateRecordResponse recordResponse =
          apiServiceImpl.createRecord(
              authToken, eventsCrescendo, new ParameterizedTypeReference<>() {}, eventUrl);
      log.info("CRESCENDO_ACTIVITY_CREATED {}", recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception ex) {
      log.error(
          "CRESCENDO_ACTIVITY_CREATE_ERROR {} , {}",
          eventsCrescendo.getExternalAccountId(),
          ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }

  @Override
  public String updateActivity(
      EventsCrescendo eventsCrescendo,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = apiServiceImpl.getTokenAndApiPath();

    return updateActivity(
        token.getAuthToken(), token.getApiPath(), eventsCrescendo, locale, bundleMessageSource);
  }

  private String updateActivity(
      AuthToken authToken,
      String apiPath,
      EventsCrescendo eventsCrescendo,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {

    try {
      String eventUpdateUrl = String.format("%s/sobjects/Event/%s", apiPath, "00UVD000007BRle2AG");

      log.info("UPDATE CRESCENDO ACTIVITY STARTED {}", eventsCrescendo);
      apiServiceImpl.updateRecord(
          authToken, eventsCrescendo, new ParameterizedTypeReference<>() {}, eventUpdateUrl);
      log.info("EVENT_UPDATED {}", eventsCrescendo);
      log.info("CRESCENDO_ACTIVITY_UPDATED {}", eventsCrescendo.getEventId());
      return eventsCrescendo.getEventId();
    } catch (Exception ex) {
      log.error(
          "CRESCENDO_ACTIVITY_UPDATE_ERROR {} , {}",
          eventsCrescendo.getExternalAccountId(),
          ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }
}
