/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum LeadSourceCrescendo {
  ADVERTISEMENT("Advertisement"),
  EMPLOYEE_REFERRAL("Employee Referral"),
  EXTERNAL_REFERRAL("External Referral"),
  PARTNER("Partner"),
  PUBLIC_RELATIONS("Public Relations"),
  SEMINAR_INTERNAL("Seminar - Internal"),
  SEMINAR_PARTNER("Seminar - Partner"),
  TRADE_SHOW("Trade Show"),
  WEB("Web"),
  WORD_OF_MOUTH("Word of mouth"),
  OTHER("Other");

  private final String value;

  @JsonCreator
  LeadSourceCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
