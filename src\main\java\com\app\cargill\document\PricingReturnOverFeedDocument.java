/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.ReturnOverFeedType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class PricingReturnOverFeedDocument extends EditableDocumentBase implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("ReturnOverFeedType")
  private ReturnOverFeedType returnOverFeedType;

  @JsonProperty("Name")
  private String name;

  @JsonProperty("Price")
  private Double price;

  @JsonProperty("Value")
  private Double value;
}
