/* Cargill Inc.(C) 2022 */
package com.app.cargill.model;

import com.app.cargill.document.DefaultAppSettingDocument;
import com.vladmihalcea.hibernate.type.json.JsonBinaryType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;

@Entity
@Table(name = "default_app_settings")
@SuperBuilder
@Data
@NoArgsConstructor
@Where(clause = "deleted = false")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DefaultAppSettings extends BaseEntity {

  @Type(JsonBinaryType.class)
  @Column(columnDefinition = "jsonb")
  private DefaultAppSettingDocument defaultAppSettingDocument;
}
