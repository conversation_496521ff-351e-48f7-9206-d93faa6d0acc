/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.ApplicationMapping;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import java.util.UUID;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;

@ExtendWith(MockitoExtension.class)
class CrescendoApplicationMappingServiceImplTest {

  @Mock private CrescendoApiServiceImpl crescendoApiService;
  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private CrescendoApplicationMappingServiceImpl service;

  private SiteMappingDocument testSiteMapping;
  private AuthToken testAuthToken;
  private AccessTokenAndApiPathDto testTokenAndApiPath;
  private CreateRecordResponse testCreateRecordResponse;

  @BeforeEach
  void setUp() {
    testSiteMapping = createTestSiteMappingDocument();
    testAuthToken = createTestAuthToken();
    testTokenAndApiPath = createTestAccessTokenAndApiPathDto();
    testCreateRecordResponse = createTestCreateRecordResponse();
  }

  @Test
  void createApplicationMapping_Success() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            eq(testAuthToken),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(testCreateRecordResponse);

    // Act
    String result =
        service.createApplicationMapping(testSiteMapping, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService)
        .createRecord(
            eq(testAuthToken),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            eq("/services/data/v58.0/sobjects/Application_Mapping__c"));
  }

  @Test
  void createApplicationMapping_ThrowsCustomDEExceptions_WhenApiServiceThrowsException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenThrow(new RuntimeException("Salesforce API error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                service.createApplicationMapping(
                    testSiteMapping, Locale.ENGLISH, bundleMessageSource));

    assertNotNull(exception);
    assertEquals("Salesforce API error", exception.getMessage());
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService)
        .createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void createApplicationMapping_ThrowsRuntimeException_WhenGetTokenAndApiPathThrowsException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath())
        .thenThrow(new RuntimeException("Token retrieval failed"));

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () ->
                service.createApplicationMapping(
                    testSiteMapping, Locale.ENGLISH, bundleMessageSource));

    assertNotNull(exception);
    assertEquals("Token retrieval failed", exception.getMessage());
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService, never())
        .createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void createApplicationMapping_WithNullSiteMapping_ThrowsNullPointerException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);

    // Act & Assert
    assertThrows(
        NullPointerException.class,
        () -> service.createApplicationMapping(null, Locale.ENGLISH, bundleMessageSource));
  }

  @Test
  void createApplicationMapping_WithNullLabyrinthSiteId_ThrowsException()
      throws JsonProcessingException, CustomDEExceptions {
    // Arrange
    testSiteMapping.setLabyrinthSiteId(null);
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);

    // Act & Assert
    assertThrows(
        CustomDEExceptions.class,
        () ->
            service.createApplicationMapping(testSiteMapping, Locale.ENGLISH, bundleMessageSource));
  }

  @Test
  void createApplicationMapping_WithDifferentLocale_Success()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(testCreateRecordResponse);

    // Act
    String result =
        service.createApplicationMapping(testSiteMapping, Locale.FRENCH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoApiService)
        .createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void createApplicationMapping_VerifyApplicationMappingFields()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(testTokenAndApiPath);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(ApplicationMapping.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(testCreateRecordResponse);

    // Act
    service.createApplicationMapping(testSiteMapping, Locale.ENGLISH, bundleMessageSource);

    // Assert
    verify(crescendoApiService)
        .createRecord(
            eq(testAuthToken),
            argThat(
                (ApplicationMapping applicationMapping) -> {
                  assertEquals("LM_SITE", applicationMapping.getExternalSystem());
                  assertEquals(
                      testSiteMapping.getLabyrinthSiteId().toString(),
                      applicationMapping.getExternalRecordNum());
                  assertEquals("001VD00000PFeA2YAL", applicationMapping.getAccountId());
                  return true;
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  private SiteMappingDocument createTestSiteMappingDocument() {
    return SiteMappingDocument.builder()
        .id(UUID.randomUUID())
        .labyrinthSiteId(UUID.fromString("1b04305c-7f6a-493e-a581-962cd8e36dee"))
        .ddwHerdId("10665")
        .maxSiteId(UUID.fromString("b7a69930-387e-40c4-a46b-4a69f6933663"))
        .labyrinthAccountId(UUID.fromString("ea9b39f0-f355-4f2d-b788-57fa96a01cfb"))
        .milkProcessorId(null)
        .dcgoId(null)
        .build();
  }

  private AuthToken createTestAuthToken() {
    AuthToken authToken = new AuthToken();
    authToken.setAccessToken("test-access-token");
    authToken.setInstanceUrl("https://test.salesforce.com");
    authToken.setIssuedAt(System.currentTimeMillis());
    return authToken;
  }

  private AccessTokenAndApiPathDto createTestAccessTokenAndApiPathDto() {
    return AccessTokenAndApiPathDto.builder()
        .authToken(testAuthToken)
        .apiPath("/services/data/v58.0")
        .build();
  }

  private CreateRecordResponse createTestCreateRecordResponse() {
    CreateRecordResponse response = new CreateRecordResponse();
    response.setId("001XXXXXXXXXXXXXXX");
    response.setSuccess(true);
    return response;
  }
}
