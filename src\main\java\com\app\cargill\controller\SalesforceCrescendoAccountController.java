/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.service.ICrescendoAccountService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/account")
@Tag(
    name = "Salesforce Crescendo Account",
    description = "Controller related to actions over Account objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoAccountController {

  private final ICrescendoAccountService crescendoAccountService;
  private final ResourceBundleMessageSource bundleMessageSource;

  @PostMapping("/create")
  @Operation(
      summary = "Create an Account directly from APP to CRESCENDO",
      description = "This api will create an account Directly from APP to Crescendo")
  public String createAccount(@RequestBody AccountDocument accountDocument)
      throws JsonProcessingException, CustomDEExceptions {
    return crescendoAccountService.createAccount(
        accountDocument, Locale.ENGLISH, bundleMessageSource);
  }

  @PatchMapping("/update")
  @Operation(
      summary = "Update an Account directly from APP to CRESCENDO",
      description = "This api will update an account Directly from APP to Crescendo")
  public String updateAccount(@RequestBody AccountDocument accountDocument)
      throws CustomDEExceptions {
    return crescendoAccountService.updateAccount(
        accountDocument, Locale.ENGLISH, bundleMessageSource);
  }
}
