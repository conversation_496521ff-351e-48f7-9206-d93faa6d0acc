/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.constants.Business;
import com.app.cargill.crescendo.constants.AccountTypeCrescendo;
import com.app.cargill.crescendo.constants.NineBoxStepTwoIDCrescendo;
import com.app.cargill.crescendo.constants.SegmentStepOneIdCrescendo;
import com.app.cargill.crescendo.constants.SubTypeIdCrescendo;
import com.app.cargill.crescendo.model.AccountsCrescendo;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.List;
import java.util.Locale;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;

@ExtendWith(MockitoExtension.class)
class CrescendoAccountServiceImplTest {

  @Mock private CrescendoApiServiceImpl crescendoApiService;

  @Mock private ICrescendoUserService crescendoUserService;

  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private CrescendoAccountServiceImpl crescendoAccountService;

  private AccountDocument accountDocument;
  private AuthToken authToken;
  private AccessTokenAndApiPathDto tokenAndApiPath;
  private SalesforceRecordsResponse<Users> userResponse;
  private Users user;
  private CreateRecordResponse createRecordResponse;

  @BeforeEach
  void setUp() {
    // Setup test data
    accountDocument = createTestAccountDocument();
    authToken = createTestAuthToken();
    tokenAndApiPath =
        AccessTokenAndApiPathDto.builder()
            .authToken(authToken)
            .apiPath("https://test.salesforce.com/services/data/v58.0")
            .build();

    user = createTestUser();
    userResponse = new SalesforceRecordsResponse<>();
    userResponse.setRecords(List.of(user));

    createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("001XXXXXXXXXXXXXXX");
    createRecordResponse.setSuccess(true);
  }

  @Test
  void createAccount_Success() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    String result =
        crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    assertEquals("001XXXXXXXXXXXXXXX", accountDocument.getGoldenRecordId());

    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/Account"));
  }

  @Test
  void createAccount_UserServiceException() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString()))
        .thenThrow(new CustomDEExceptions("User not found", HttpStatus.SC_NOT_FOUND));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoAccountService.createAccount(
                    accountDocument, Locale.ENGLISH, bundleMessageSource));

    assertEquals("User not found", exception.getMessage());
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoApiService, never()).createRecord(any(), any(), any(), any());
  }

  @Test
  void createAccount_ApiServiceException() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenThrow(new RuntimeException("API Error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoAccountService.createAccount(
                    accountDocument, Locale.ENGLISH, bundleMessageSource));

    assertEquals("API Error", exception.getMessage());
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoApiService).createRecord(any(), any(), any(), any());
  }

  @Test
  void updateAccount_Success() throws CustomDEExceptions {
    // Arrange
    accountDocument.setGoldenRecordId("001XXXXXXXXXXXXXXX");
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    doNothing()
        .when(crescendoApiService)
        .updateRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString());

    // Act
    String result =
        crescendoAccountService.updateAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);

    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoApiService)
        .updateRecord(
            eq(authToken),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/Account/001XXXXXXXXXXXXXXX"));
  }

  @Test
  void updateAccount_Exception() throws CustomDEExceptions {
    // Arrange
    accountDocument.setGoldenRecordId("001XXXXXXXXXXXXXXX");
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    doThrow(new RuntimeException("Update failed"))
        .when(crescendoApiService)
        .updateRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString());

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                crescendoAccountService.updateAccount(
                    accountDocument, Locale.ENGLISH, bundleMessageSource));

    assertEquals("Update failed", exception.getMessage());
    verify(crescendoApiService).getTokenAndApiPath();
    verify(crescendoUserService).getByUserEmail("<EMAIL>");
    verify(crescendoApiService).updateRecord(any(), any(), any(), any());
  }

  @Test
  void documentToCrescendo_WithAllFields() throws Exception {
    // Arrange
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert - Verify the account mapping through the API call
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            argThat(
                account -> {
                  AccountsCrescendo acc = (AccountsCrescendo) account;
                  return acc.getName().equals("Test Account")
                      && acc.getAccountType()
                          .equals(AccountTypeCrescendo.COMMERCIAL_CUSTOMER.getValue())
                      && acc.isActive()
                      && acc.getAutoValidate()
                      && "Test City".equals(acc.getBillingCity())
                      && "USA".equals(acc.getBillingCountry())
                      && "12345".equals(acc.getBillingPostalCode())
                      && "Test State".equals(acc.getBillingState())
                      && acc.getBusiness().equals("US")
                      && acc.getBusinessUnit().equals("US")
                      && acc.getCustomerCode().equals("CUST001")
                      && acc.getExternalId().equals(accountDocument.getId().toString())
                      && acc.getGoldenRecordName() == null
                      && !acc.getIsDuplicate()
                      && acc.isMobileFirst()
                      && acc.getOwnerId().equals("005XXXXXXXXXXXXXXX")
                      && !acc.isProspectValidated()
                      && acc.getRecordTypeId().equals("012XXXXXXXXXXXXXXX")
                      && acc.getSegmentStep1().equals(SegmentStepOneIdCrescendo.NOAH.getValue())
                      && acc.getSourceSystem().equals("LM")
                      && acc.getType().equals(SubTypeIdCrescendo.FARM_PRODUCER.getValue())
                      && acc.getX9BoxStep2()
                          .equals(NineBoxStepTwoIDCrescendo.LARGE_EXTENSIVE.getValue());
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void documentToCrescendo_WithNullPhysicalAddress() throws Exception {
    // Arrange
    accountDocument.setPhysicalAddress(null);
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert - Verify billing address fields are null when physical address is null
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            argThat(
                account -> {
                  AccountsCrescendo acc = (AccountsCrescendo) account;
                  return acc.getBillingCity() == null
                      && acc.getBillingCountry() == null
                      && acc.getBillingPostalCode() == null
                      && acc.getBillingState() == null;
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void documentToCrescendo_WithNullSegmentStepOneId() throws Exception {
    // Arrange
    accountDocument.setSegmentStepOneId(null);
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert - Verify default segment is set when null
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            argThat(
                account -> {
                  AccountsCrescendo acc = (AccountsCrescendo) account;
                  return acc.getSegmentStep1().equals(SegmentStepOneIdCrescendo.NOAH.getValue());
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void documentToCrescendo_WithEmptySegmentStepOneId() throws Exception {
    // Arrange
    accountDocument.setSegmentStepOneId("");
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert - Verify default segment is set when empty
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            argThat(
                account -> {
                  AccountsCrescendo acc = (AccountsCrescendo) account;
                  return acc.getSegmentStep1().equals(SegmentStepOneIdCrescendo.NOAH.getValue());
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void documentToCrescendo_WithNullNineBoxStepTwoID() throws Exception {
    // Arrange
    accountDocument.setNineBoxStepTwoID(null);
    when(crescendoApiService.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoUserService.getByUserEmail(anyString())).thenReturn(userResponse);
    when(crescendoApiService.createRecord(
            any(AuthToken.class),
            any(AccountsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    crescendoAccountService.createAccount(accountDocument, Locale.ENGLISH, bundleMessageSource);

    // Assert - Verify X9BoxStep2 is null when nineBoxStepTwoID is null
    verify(crescendoApiService)
        .createRecord(
            eq(authToken),
            argThat(
                account -> {
                  AccountsCrescendo acc = (AccountsCrescendo) account;
                  return acc.getX9BoxStep2() == null;
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  private AccountDocument createTestAccountDocument() {
    return AccountDocument.builder()
        .id(UUID.randomUUID())
        .accountName("Test Account")
        .accountType(1) // COMMERCIAL_CUSTOMER
        .ownerId("<EMAIL>")
        .type("012XXXXXXXXXXXXXXX")
        .physicalAddress(
            Address.builder()
                .city("Test City")
                .country("USA")
                .postalCode("12345")
                .stateOrProvince("Test State")
                .build())
        .businessID(Business.US.getBusinessId())
        .customerCode("CUST001")
        .segmentStepOneId("Noah")
        .nineBoxStepTwoID("1 - Large/Extensive")
        .build();
  }

  private AuthToken createTestAuthToken() {
    AuthToken token = new AuthToken();
    token.setAccessToken("test_access_token");
    token.setInstanceUrl("https://test.salesforce.com");
    token.setTokenType("Bearer");
    return token;
  }

  private Users createTestUser() {
    Users testUser = new Users();
    testUser.setId("005XXXXXXXXXXXXXXX");
    testUser.setName("Test User");
    testUser.setEmail("<EMAIL>");
    testUser.setIsActive(true);
    return testUser;
  }
}
