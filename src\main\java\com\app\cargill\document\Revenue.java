/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class Revenue implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("BfRevenue")
  private Double bfRevenue;

  @JsonProperty("ProteinRevenue")
  private Double proteinRevenue;

  @JsonProperty("OtherSolidsRevenue")
  private Double otherSolidsRevenue;

  @JsonProperty("Subtotal")
  private Double subtotal;

  @JsonProperty("DeductionsPricePerCowPerDay")
  private Double deductionsPricePerCowPerDay;

  @JsonProperty("SnfNonPayment")
  private Double snfNonPayment;

  @JsonProperty("TotalRevenueCowDay")
  private Double totalRevenueCowDay;

  @JsonProperty("TotalRevenuePricePerKgButterFat")
  private Double totalRevenuePricePerKgButterFat;

  @JsonProperty("TotalRevenuePerHl")
  private Double totalRevenuePerHl;

  // Getters and setters...
}
