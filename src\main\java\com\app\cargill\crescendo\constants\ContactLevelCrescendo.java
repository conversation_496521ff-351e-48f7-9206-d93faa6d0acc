/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ContactLevelCrescendo {
  PRIMARY("Primary"),
  SECONDARY("Secondary"),
  TERTIARY("Tertiary");

  private final String value;

  @JsonCreator
  ContactLevelCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
