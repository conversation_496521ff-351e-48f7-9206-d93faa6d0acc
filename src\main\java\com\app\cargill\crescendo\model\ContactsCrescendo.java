/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class ContactsCrescendo implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("LastName")
  private String lastName;

  @JsonProperty("FirstName")
  private String firstName;

  @JsonProperty("Phone")
  private String phone;

  @JsonProperty("External_Id__c")
  private String externalId;

  @JsonProperty("Mobile_First__c")
  private Boolean mobileFirst;

  @JsonProperty("email")
  private String email;
}
