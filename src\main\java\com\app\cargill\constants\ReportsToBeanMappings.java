/* Cargill Inc.(C) 2022 */
package com.app.cargill.constants;

import com.app.cargill.service.impl.BCSHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.BCSPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.BcsAnimalAnalysisReportServiceImpl;
import com.app.cargill.service.impl.CudChewingHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.CudChewingPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.ForagePennStateHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.HeatStressReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScoreAnimalAnalysisExcelReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScoreHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.LocomotionScorePenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.MetabolicIncidenceReportServiceImpl;
import com.app.cargill.service.impl.MilkSoldEvaluationReportServiceImpl;
import com.app.cargill.service.impl.PenTimeBudgetPotentialMilkLossGainReportServiceImpl;
import com.app.cargill.service.impl.PenTimeBudgetTimeAvailableForRestingReportServiceImpl;
import com.app.cargill.service.impl.ProfitabilityAnalysisReportServiceImpl;
import com.app.cargill.service.impl.ReturnOverFeedPerKgButterFatReportServiceImpl;
import com.app.cargill.service.impl.ReturnOverFeedReportServiceImpl;
import com.app.cargill.service.impl.RoboticMilkEvaluationReportServiceImpl;
import com.app.cargill.service.impl.RumenFillHealthHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenFillHealthPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthMSHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthMSPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthManureScreeningPenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl;
import com.app.cargill.service.impl.RumenHealthTMRParticleScorePenAnalysisReportServiceImpl;
import com.app.cargill.service.impl.VisitReportServiceImpl;
import lombok.Getter;

@SuppressWarnings({"java:S7091", "java:S1874", "java:S6548", "javaarchitecture:S7091"})
public enum ReportsToBeanMappings {
  BCS_HERD_ANALYSIS_REPORT(
      BCSHerdAnalysisReportServiceImpl.class, "BCSHerdAnalysis.ftl", null, "BCS Herd Analysis"),
  BCS_PEN_ANALYSIS_REPORT(
      BCSPenAnalysisReportServiceImpl.class, "BCSPenAnalysis.ftl", null, "BCS Pen Analysis"),
  BCS_ANIMAL_ANALYSIS_REPORT(
      BcsAnimalAnalysisReportServiceImpl.class,
      "BCSAnimalAnalysis_BCS.ftl",
      "BCSAnimalAnalysis_LocomotionScore.ftl",
      "BCS Animal Analysis"),
  CUD_CHEWING_PEN_ANALYSIS_REPORT(
      CudChewingPenAnalysisReportServiceImpl.class,
      "CudChewingPenAnalysis.ftl",
      null,
      "Cud Chewing Pen Analysis"),
  CUD_CHEWING_HERD_ANALYSIS_REPORT(
      CudChewingHerdAnalysisReportServiceImpl.class,
      "CudChewingHerdAnalysis_CudChewingPercentage.ftl",
      "CudChewingHerdAnalysis_NoOfChews.ftl",
      "Cud Chewing Herd Analysis"),
  LOCOMOTION_SCORE_PEN_ANALYSIS_REPORT(
      LocomotionScorePenAnalysisReportServiceImpl.class,
      "LocomotionScorePenAnalysis.ftl",
      null,
      "Locomotion Score Pen Analysis"),
  LOCOMOTION_SCORE_HERD_ANALYSIS_REPORT(
      LocomotionScoreHerdAnalysisReportServiceImpl.class,
      "LocomotionScoreHerdAnalysis.ftl",
      null,
      "Locomotion Score Herd Analysis"),
  LOCOMOTION_SCORE_ANIMAL_ANALYSIS_REPORT(
      LocomotionScoreAnimalAnalysisExcelReportServiceImpl.class,
      "LocomotionScoreAnimalAnalysis_BCS.ftl",
      "LocomotionScoreAnimalAnalysis_LocomotionScore.ftl",
      "Locomotion Score Animal Analysis"),
  RUMEN_HEALTH_MS_HERD_ANALYSIS_REPORT(
      RumenHealthMSHerdAnalysisReportServiceImpl.class,
      "RumenHealthManureScoreHerdAnalysis.ftl",
      null,
      "Rumen Health Manure Score Herd Analysis"),
  RUMEN_HEALTH_MS_PEN_ANALYSIS_REPORT(
      RumenHealthMSPenAnalysisReportServiceImpl.class,
      "RumenHealthMSPenAnalysis.ftl",
      null,
      "Rumen Health Manure Score Pen Analysis"),
  MILK_SOLD_EVALUATION_REPORT(
      MilkSoldEvaluationReportServiceImpl.class,
      "MilkSoldEvaluation.ftl",
      null,
      "Milk Sold Evaluation"),
  ROBOTIC_MILK_EVALUATION_REPORT(
      RoboticMilkEvaluationReportServiceImpl.class,
      "RoboticMilkEvaluation_DualYAxis.ftl",
      "RoboticMilkEvaluation_SingleYAxis.ftl",
      "Cud Chewing Herd Analysis"),
  METABOLIC_INCIDENCE_REPORT(
      MetabolicIncidenceReportServiceImpl.class,
      "MetabolicIncidence_Percentage.ftl",
      "MetabolicIncidence_MetabolicDisorderCostPerCow.ftl",
      "Metabolic Incidence"),
  RUMEN_FILL_HEALTH_PEN_ANALYSIS_REPORT(
      RumenFillHealthPenAnalysisReportServiceImpl.class,
      "RumenFillHealthPenAnalysis.ftl",
      null,
      "Rumen Fill Pen Analysis"),
  RUMEN_FILL_HEALTH_HERD_ANALYSIS_REPORT(
      RumenFillHealthHerdAnalysisReportServiceImpl.class,
      "RumenFillHealthHerdAnalysis.ftl",
      null,
      "Rumen Fill Herd Analysis"),
  FORAGE_PENN_STATE_HERD_ANALYSIS_REPORT(
      ForagePennStateHerdAnalysisReportServiceImpl.class,
      "ForagePennStateHerdAnalysis.ftl",
      null,
      "Forage Penn State Herd Analysis"),
  RUMEN_HEALTH_TMR_PARTICLE_SCORE_PEN_ANALYSIS_REPORT(
      RumenHealthTMRParticleScorePenAnalysisReportServiceImpl.class,
      "RumenHealthTMRParticleScorePenAnalysis.ftl",
      null,
      "Rumen Health TMR Particle Score Pen Analysis"),
  RUMEN_HEALTH_TMR_PARTICLE_SCORE_HERD_ANALYSIS_REPORT(
      RumenHealthTMRParticleScoreHerdAnalysisReportServiceImpl.class,
      "RumenHealthTMRParticleScoreHerdAnalysis.ftl",
      null,
      "Rumen Health TMR Particle Score Herd Analysis"),
  PEN_TIME_BUDGET_POTENTIAL_MILK_LOSS_GAIN_REPORT(
      PenTimeBudgetPotentialMilkLossGainReportServiceImpl.class,
      "PenTimeBudget_SingleYAxis.ftl",
      null,
      "Pen Time Potential Milk Loss Gain Budget Report"),
  PEN_TIME_BUDGET_TIME_AVAILABLE_FOR_RESTING_REPORT(
      PenTimeBudgetTimeAvailableForRestingReportServiceImpl.class,
      "PenTimeBudget_TimeAvailableForResting.ftl",
      null,
      "Pen Time Budget Time Available For Resting"),
  HEAT_STRESS_REPORT(
      HeatStressReportServiceImpl.class, "HeatStress_Report.ftl", null, "Heat Stress"),

  VISIT_REPORT(VisitReportServiceImpl.class, "VisitReport.ftl", null, "Visit Report"),

  RUMEN_HEALTH_MANURE_SCREENING_PEN_ANALYSIS_REPORT(
      RumenHealthManureScreeningPenAnalysisReportServiceImpl.class,
      "RumenHealthManureScreeningPenAnalysisReportServiceImpl.ftl",
      null,
      "Rumen Health Manure Screening Pen Analysis"),
  PROFITABILITY_ANALYSIS_TOTAL_PRODUCTION(
      ProfitabilityAnalysisReportServiceImpl.class,
      "ProfitabilityAnalysisTotalProduction.ftl",
      null,
      "Profitability Analysis Total Production"),
  PROFITABILITY_ANALYSIS_PRODUCTION_IN_150_DIM(
      ProfitabilityAnalysisReportServiceImpl.class,
      "ProfitabilityAnalysisProductionInDim.ftl",
      null,
      "Profitability Analysis Production in 150 DIM"),
  PROFITABILITY_ANALYSIS_MILK_PRICE_FEEDING_COST(
      ProfitabilityAnalysisReportServiceImpl.class,
      "ProfitabilityAnalysisMilkPriceFeedingCost.ftl",
      null,
      "Profitability Analysis Milk Price Feeding Cost"),
  PROFITABILITY_ANALYSIS_REVENUE_PER_COW_PER_DAY(
      ProfitabilityAnalysisReportServiceImpl.class,
      "ProfitabilityAnalysisRevenuePerCowPerDay.ftl",
      null,
      "Profitability Analysis Revenue Per Cow Per Day"),
  RETURN_OVER_FEED(
      ReturnOverFeedReportServiceImpl.class, "ReturnOverFeedCost.ftl", null, "Return Over Feed"),
  RETURN_OVER_FEED_PER_KG_BUTTERFAT(
      ReturnOverFeedPerKgButterFatReportServiceImpl.class,
      "ReturnOverFeedPerKgButterFat.ftl",
      null,
      "Return Over Feed Per Kg Butter Fat");

  @Getter private final java.lang.Class<?> clazz;
  @Getter private final String imageTemplateName0;
  @Getter private final String imageTemplateName1;
  @Getter private final String fileName;

  ReportsToBeanMappings(
      java.lang.Class<?> clazz,
      String imageTemplateName0,
      String imageTemplateName1,
      String fileName) {
    this.clazz = clazz;
    this.imageTemplateName0 = imageTemplateName0;
    this.imageTemplateName1 = imageTemplateName1;
    this.fileName = fileName;
  }
}
