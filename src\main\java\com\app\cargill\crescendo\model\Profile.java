/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class Profile implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  private Attributes attributes;

  @JsonProperty("UserLicense")
  private UserLicense userLicense;
}
