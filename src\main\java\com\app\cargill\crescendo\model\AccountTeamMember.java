/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class AccountTeamMember implements Serializable {
  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("Id")
  private String id;

  @JsonProperty("UserId")
  private String userId;

  @JsonProperty("AccountId")
  private String accountId;

  @JsonProperty("TeamMemberRole")
  private String teamMemberRole;

  @JsonProperty("AccountAccessLevel")
  private String accountAccessLevel;

  @JsonProperty("OpportunityAccessLevel")
  private String opportunityAccessLevel;

  @JsonProperty("CaseAccessLevel")
  private String caseAccessLevel;

  @JsonProperty("ContactAccessLevel")
  private String contactAccessLevel;
}
