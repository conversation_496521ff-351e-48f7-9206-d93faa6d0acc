/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum SegmentStepOneIdCrescendo {
  NOT_DEFINED("0 - Not Defined"),
  AIDEN("<PERSON>"),
  MILA("<PERSON><PERSON>"),
  SPENCE("<PERSON>"),
  KOBE("Kobe"),
  WALTON("<PERSON>"),
  NOAH("<PERSON>"),
  SONYA("Sonya"),
  DENNIS("<PERSON>"),
  <PERSON><PERSON><PERSON>("Baxter"),
  END_USER("End User");

  private final String value;

  @JsonCreator
  SegmentStepOneIdCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static SegmentStepOneIdCrescendo fromValue(String input) {
    for (SegmentStepOneIdCrescendo v : SegmentStepOneIdCrescendo.values()) {
      if (v.getValue().equalsIgnoreCase(input)) {
        return v;
      }
    }
    return null;
  }
}
