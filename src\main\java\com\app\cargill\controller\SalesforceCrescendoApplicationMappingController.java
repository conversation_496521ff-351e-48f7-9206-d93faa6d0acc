/* Cargill Inc.(C) 2022 */
package com.app.cargill.controller;

import com.app.cargill.crescendo.service.ICrescendoApplicationMappingService;
import com.app.cargill.document.SiteMappingDocument;
import com.app.cargill.exceptions.CustomDEExceptions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/salesforce/crescendo/applicationMapping")
@Tag(
    name = "Salesforce Crescendo Application Mapping",
    description = "Controller related to actions over Application Mappings objects in Crescendo")
@RequiredArgsConstructor
@Slf4j
public class SalesforceCrescendoApplicationMappingController {

  private final ICrescendoApplicationMappingService crescendoApplicationMappingService;
  private final ResourceBundleMessageSource bundleMessageSource;

  @PostMapping
  @Operation(
      summary = "Create Application Mapping from DE APP TO Crescendo",
      description = "Create Application Mapping from DE APP TO Crescendo")
  public String createApplicationMapping(@RequestBody SiteMappingDocument siteMapping)
      throws CustomDEExceptions {
    return crescendoApplicationMappingService.createApplicationMapping(
        siteMapping, Locale.ENGLISH, bundleMessageSource);
  }
}
