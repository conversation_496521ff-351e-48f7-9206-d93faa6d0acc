/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.crescendo.model.ContactsCrescendo;
import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import java.util.Locale;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@SuppressWarnings(
    "java:S1172") // Added for unused variables, which will be incorporated during translations
// integration
public class CrescendoContactServiceImpl implements ICrescendoContactService {

  private final CrescendoApiServiceImpl crescendoApiServiceImpl;

  @Override
  public String createContact(
      Contact contact, Locale locale, ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = crescendoApiServiceImpl.getTokenAndApiPath();
    return createContact(
        token.getApiPath(), token.getAuthToken(), contact, bundleMessageSource, locale);
  }

  private String createContact(
      String apiPath,
      AuthToken authToken,
      Contact contact,
      ResourceBundleMessageSource bundleMessageSource,
      Locale locale)
      throws CustomDEExceptions {
    try {
      String contactUrl = String.format("%s/sobjects/Contact", apiPath);

      ContactsCrescendo contactCrescendo = documentToCrescendo(contact);
      log.info("CREATED CRESCENDO CONTACT STARTED {}", contactCrescendo);
      CreateRecordResponse recordResponse =
          crescendoApiServiceImpl.createRecord(
              authToken, contactCrescendo, new ParameterizedTypeReference<>() {}, contactUrl);
      log.info("CRESCENDO_CONTACT_CREATED {}", recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception ex) {
      log.error("CRESCENDO_CONTACT_CREATE_ERROR {} , {}", contact.getContactId(), ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }

  private ContactsCrescendo documentToCrescendo(Contact contact) {
    ContactsCrescendo contactsCrescendo = new ContactsCrescendo();

    contactsCrescendo.setAccountId(contact.getGoldenRecordAcountId());
    contactsCrescendo.setFirstName(contact.getFirstName());
    contactsCrescendo.setLastName(contact.getLastName());
    contactsCrescendo.setExternalId(contact.getContactId().toString());
    contactsCrescendo.setMobileFirst(true);
    contactsCrescendo.setPhone(contact.getPhoneNumber());
    contactsCrescendo.setEmail(contact.getEmailAddress());

    return contactsCrescendo;
  }

  @Override
  public String updateContact(
      Contact contact, Locale locale, ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = crescendoApiServiceImpl.getTokenAndApiPath();

    return updateContact(
        token.getAuthToken(), token.getApiPath(), contact, locale, bundleMessageSource);
  }

  private String updateContact(
      AuthToken authToken,
      String apiPath,
      Contact contact,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    try {
      String contactUpdateUrl =
          String.format("%s/sobjects/Contact/%s", apiPath, contact.getSFDCContactId());

      ContactsCrescendo contactsCrescendo = documentToCrescendo(contact);

      log.info("UPDATE_CRESCENDO_CONTACT {}", contactsCrescendo);
      crescendoApiServiceImpl.updateRecord(
          authToken, contactsCrescendo, new ParameterizedTypeReference<>() {}, contactUpdateUrl);
      return contact.getSFDCContactId();
    } catch (Exception ex) {
      log.error(
          "Error updating Crescendo Contact: {} , {}", contact.getSFDCContactId(), ex.getMessage());
      throw new CustomDEExceptions(ex.getMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }
}
