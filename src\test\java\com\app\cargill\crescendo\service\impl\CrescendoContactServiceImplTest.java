/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.ContactsCrescendo;
import com.app.cargill.document.Contact;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import java.util.UUID;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;

@ExtendWith(MockitoExtension.class)
class CrescendoContactServiceImplTest {

  @Mock private CrescendoApiServiceImpl crescendoApiServiceImpl;

  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private CrescendoContactServiceImpl service;

  private Contact testContact;
  private AuthToken authToken;
  private AccessTokenAndApiPathDto tokenAndApiPath;
  private CreateRecordResponse createRecordResponse;

  @BeforeEach
  void setUp() {
    testContact = createTestContact();
    authToken = createTestAuthToken();
    tokenAndApiPath =
        AccessTokenAndApiPathDto.builder()
            .authToken(authToken)
            .apiPath("https://test.salesforce.com/services/data/v58.0")
            .build();

    createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("003XXXXXXXXXXXXXXX");
    createRecordResponse.setSuccess(true);
  }

  @Test
  void createContact_Success() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    String result = service.createContact(testContact, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("003XXXXXXXXXXXXXXX", result);
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/Contact"));
  }

  @Test
  void createContact_VerifyMapping() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    service.createContact(testContact, Locale.ENGLISH, bundleMessageSource);

    // Assert
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            argThat(
                contact -> {
                  ContactsCrescendo cc = (ContactsCrescendo) contact;
                  return cc.getAccountId().equals("001XXXXXXXXXXXXXXX")
                      && cc.getFirstName().equals("John")
                      && cc.getLastName().equals("Doe")
                      && cc.getExternalId().equals(testContact.getContactId().toString())
                      && cc.getMobileFirst().equals(true)
                      && cc.getPhone().equals("************")
                      && cc.getId() == null; // ID should not be set for create operations
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void createContact_WithNullSFDCContactId() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    testContact.setSFDCContactId(null);
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    service.createContact(testContact, Locale.ENGLISH, bundleMessageSource);

    // Assert
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            argThat(
                contact -> {
                  ContactsCrescendo cc = (ContactsCrescendo) contact;
                  return cc.getId() == null;
                }),
            any(ParameterizedTypeReference.class),
            anyString());
  }

  @Test
  void createContact_ApiServiceException() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenThrow(new RuntimeException("API Error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> service.createContact(testContact, Locale.ENGLISH, bundleMessageSource));

    assertEquals("API Error", exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl).createRecord(any(), any(), any(), any());
  }

  @Test
  void updateContact_Success() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    doNothing()
        .when(crescendoApiServiceImpl)
        .updateRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString());

    // Act
    String result = service.updateContact(testContact, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("003XXXXXXXXXXXXXXX", result);
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .updateRecord(
            eq(authToken),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/Contact/003XXXXXXXXXXXXXXX"));
  }

  @Test
  void updateContact_VerifyUrlFormat() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    doNothing()
        .when(crescendoApiServiceImpl)
        .updateRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString());

    // Act
    service.updateContact(testContact, Locale.ENGLISH, bundleMessageSource);

    // Assert
    verify(crescendoApiServiceImpl)
        .updateRecord(
            eq(authToken),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            eq(
                "https://test.salesforce.com/services/data/v58.0/sobjects/Contact/003XXXXXXXXXXXXXXX"));
  }

  @Test
  void updateContact_ApiServiceException() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    doThrow(new RuntimeException("Update failed"))
        .when(crescendoApiServiceImpl)
        .updateRecord(
            any(AuthToken.class),
            any(ContactsCrescendo.class),
            any(ParameterizedTypeReference.class),
            anyString());

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () -> service.updateContact(testContact, Locale.ENGLISH, bundleMessageSource));

    assertEquals("Update failed", exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl).updateRecord(any(), any(), any(), any());
  }

  private Contact createTestContact() {
    return Contact.builder()
        .contactId(UUID.randomUUID())
        .goldenRecordAcountId("001XXXXXXXXXXXXXXX")
        .sFDCContactId("003XXXXXXXXXXXXXXX")
        .firstName("John")
        .lastName("Doe")
        .phoneNumber("************")
        .build();
  }

  private AuthToken createTestAuthToken() {
    AuthToken token = new AuthToken();
    token.setAccessToken("test_access_token");
    token.setInstanceUrl("https://test.salesforce.com");
    token.setTokenType("Bearer");
    return token;
  }
}
