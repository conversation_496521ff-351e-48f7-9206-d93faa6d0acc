# User Journey Analytics Implementation

This document describes the implementation of the User Journey Analytics feature for the Dairy Enteligen Service APIs.

## Overview

The User Journey Analytics feature allows mobile applications to send user interaction data and provides an Excel export functionality for analytics purposes.

## Implementation Details

### 1. Database Structure

**Table**: `user_journey`
- Extends `BaseEntity` (includes id, created_date, updated_date, deleted, local_id)
- Contains a JSONB column `user_journey_document` with the following structure:

```json
{
  "UserEmail": "<EMAIL>",
  "Paths": [
    {
      "EventName": "DASHBOARD_NOTES",
      "Path": "dashboard/notes", 
      "EventTriggerTime": "12-15-20"
    }
  ]
}
```

### 2. API Endpoints

#### POST `/user-journey-analytics`
Saves user journey data from mobile applications.

**Request Body**:
```json
{
  "userEmail": "<EMAIL>",
  "paths": [
    {
      "eventName": "DASHBOARD_NOTES",
      "path": "dashboard/notes",
      "eventTriggerTime": "12-15-20"
    },
    {
      "eventName": "CUSTOMER_SITES_NOTES", 
      "path": "dashboard/customers/sites/notes",
      "eventTriggerTime": "12-15-20"
    }
  ]
}
```

**Response**:
```json
{
  "data": {
    "id": "uuid",
    "userEmail": "<EMAIL>",
    "paths": [...],
    "createdDate": "2023-...",
    "updatedDate": "2023-..."
  },
  "message": "User journey data saved successfully",
  "status": "SUCCESS"
}
```

#### GET `/user-journey-analytics/export?lastUpdatedDate={instant}`
Exports user journey data to Excel format.

**Parameters**:
- `lastUpdatedDate`: ISO instant format (e.g., "2023-12-01T00:00:00Z")
- `Accept-Language`: Optional header for localization (default: "EN")

**Response**: Excel file (.xlsx) with columns:
- User Email
- Event Name  
- Path
- Event Trigger Time

### 3. Files Created

#### Entities and Documents
- `UserJourney.java` - JPA entity
- `UserJourneyDocument.java` - JSON document structure
- `UserJourneyPath.java` - Path data structure

#### DTOs
- `UserJourneyDto.java` - Response DTO
- `UserJourneyRequestDto.java` - Request DTO  
- `UserJourneyPathDto.java` - Path DTO

#### Repository
- `UserJourneyRepository.java` - Data access layer

#### Services
- `IUserJourneyService.java` - Service interface
- `UserJourneyServiceImpl.java` - Service implementation
- `UserJourneyExcelReportServiceImpl.java` - Excel export service

#### Controller
- `UserJourneyAnalyticsController.java` - REST endpoints

#### Tests
- `UserJourneyAnalyticsControllerTest.java` - Controller tests
- `UserJourneyServiceImplTest.java` - Service tests

### 4. Key Features

- **Data Persistence**: Uses PostgreSQL with JSONB for flexible schema
- **Excel Export**: Apache POI integration for Excel file generation
- **Filtering**: Export based on last updated date
- **Localization**: Support for multiple languages
- **Error Handling**: Comprehensive error handling and logging
- **Testing**: Unit tests for core functionality

### 5. Usage Examples

#### Saving Data (cURL)
```bash
curl -X POST http://localhost:8080/user-journey-analytics \
  -H "Content-Type: application/json" \
  -d '{
    "userEmail": "<EMAIL>",
    "paths": [
      {
        "eventName": "DASHBOARD_NOTES",
        "path": "dashboard/notes", 
        "eventTriggerTime": "12-15-20"
      }
    ]
  }'
```

#### Exporting Data (cURL)
```bash
curl -X GET "http://localhost:8080/user-journey-analytics/export?lastUpdatedDate=2023-12-01T00:00:00Z" \
  -H "Accept-Language: EN" \
  --output user_journey_analytics.xlsx
```

### 6. Database Migration

The following SQL will be needed to create the table:

```sql
CREATE TABLE user_journey (
    id BIGSERIAL PRIMARY KEY,
    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,
    local_id VARCHAR(255),
    user_journey_document JSONB
);

CREATE INDEX idx_user_journey_updated_date ON user_journey(updated_date);
CREATE INDEX idx_user_journey_user_email ON user_journey USING GIN ((user_journey_document->>'UserEmail'));
```

## Notes

- The implementation follows the existing codebase patterns and conventions
- All classes use Lombok for boilerplate code reduction
- The Excel export uses the same utility classes as other reports in the system
- Error handling and logging are consistent with the existing codebase
- The API follows RESTful conventions and includes proper Swagger documentation
