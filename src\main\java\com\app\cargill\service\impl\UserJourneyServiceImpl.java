/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.document.UserJourneyDocument;
import com.app.cargill.document.UserJourneyPath;
import com.app.cargill.dto.UserJourneyDto;
import com.app.cargill.dto.UserJourneyPathDto;
import com.app.cargill.dto.UserJourneyRequestDto;
import com.app.cargill.model.UserJourney;
import com.app.cargill.repository.UserJourneyRepository;
import com.app.cargill.service.IUserJourneyService;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class UserJourneyServiceImpl implements IUserJourneyService {

  private final UserJourneyRepository userJourneyRepository;

  @Override
  public UserJourneyDto save(UserJourneyRequestDto userJourneyRequestDto) {
    log.debug("Saving user journey data for user: {}", userJourneyRequestDto.getUserEmail());

    UserJourney userJourney = dtoToModel(userJourneyRequestDto);

    return modelToDto(userJourneyRepository.save(userJourney));
  }

  @Override
  public List<UserJourneyDto> getByUpdatedDateAfter(Instant lastUpdatedDate) {
    log.debug("Fetching user journey data updated after: {}", lastUpdatedDate);

    List<UserJourney> userJourneys = userJourneyRepository.findByUpdatedDateAfter(lastUpdatedDate);

    return userJourneys.stream().map(this::modelToDto).toList();
  }

  private UserJourney dtoToModel(UserJourneyRequestDto dto) {

    List<UserJourneyPath> paths = new ArrayList<>();
    dto.getPaths().stream()
        .forEach(
            pathDto -> {
              UserJourneyPath path =
                  UserJourneyPath.builder()
                      .path(pathDto.getPath())
                      .eventName(pathDto.getEventName())
                      .eventTriggerStartTime(pathDto.getEventTriggerStartTime())
                      .eventTriggerEndTime(pathDto.getEventTriggerEndTime())
                      .eventDuration(pathDto.getEventDuration())
                      .build();
              paths.add(path);
            });

    UserJourneyDocument document =
        UserJourneyDocument.builder()
            .id(UUID.randomUUID())
            .userEmail(dto.getUserEmail())
            .userCountry(dto.getUserCountry())
            .paths(paths)
            .build();

    return UserJourney.builder().userJourneyDocument(document).build();
  }

  private UserJourneyDto modelToDto(UserJourney userJourney) {

    List<UserJourneyPathDto> pathDtos = new ArrayList<>();
    userJourney.getUserJourneyDocument().getPaths().stream()
        .forEach(
            path -> {
              UserJourneyPathDto pathDto =
                  UserJourneyPathDto.builder()
                      .path(path.getPath())
                      .eventName(path.getEventName())
                      .eventTriggerStartTime(path.getEventTriggerStartTime())
                      .eventTriggerEndTime(path.getEventTriggerEndTime())
                      .eventDuration(path.getEventDuration())
                      .build();
              pathDtos.add(pathDto);
            });

    return UserJourneyDto.builder()
        .id(userJourney.getUserJourneyDocument().getId())
        .userEmail(userJourney.getUserJourneyDocument().getUserEmail())
        .userCountry(userJourney.getUserJourneyDocument().getUserCountry())
        .paths(pathDtos)
        .createdDate(
            userJourney.getCreatedDate() != null ? userJourney.getCreatedDate().toInstant() : null)
        .updatedDate(
            userJourney.getUpdatedDate() != null ? userJourney.getUpdatedDate().toInstant() : null)
        .deleted(userJourney.isDeleted())
        .build();
  }
}
