/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.model;

import static org.junit.jupiter.api.Assertions.*;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

class ContactsCrescendoTest {

  @Test
  void testContactsCrescendoSerialization() throws JsonProcessingException {
    // Arrange
    ContactsCrescendo contact = new ContactsCrescendo();
    contact.setId("003XXXXXXXXXXXXXXX");
    contact.setAccountId("001XXXXXXXXXXXXXXX");
    contact.setLastName("Doe");
    contact.setFirstName("John");
    contact.setPhone("************");
    contact.setExternalId("ext-123");
    contact.setMobileFirst(true);

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    String json = objectMapper.writeValueAsString(contact);
    ContactsCrescendo deserializedContact = objectMapper.readValue(json, ContactsCrescendo.class);

    // Assert
    assertNotNull(json);
    assertTrue(json.contains("\"Id\":\"003XXXXXXXXXXXXXXX\""));
    assertTrue(json.contains("\"AccountId\":\"001XXXXXXXXXXXXXXX\""));
    assertTrue(json.contains("\"LastName\":\"Doe\""));
    assertTrue(json.contains("\"FirstName\":\"John\""));
    assertTrue(json.contains("\"Phone\":\"************\""));
    assertTrue(json.contains("\"External_Id__c\":\"ext-123\""));
    assertTrue(json.contains("\"Mobile_First__c\":true"));

    assertEquals(contact.getId(), deserializedContact.getId());
    assertEquals(contact.getAccountId(), deserializedContact.getAccountId());
    assertEquals(contact.getLastName(), deserializedContact.getLastName());
    assertEquals(contact.getFirstName(), deserializedContact.getFirstName());
    assertEquals(contact.getPhone(), deserializedContact.getPhone());
    assertEquals(contact.getExternalId(), deserializedContact.getExternalId());
    assertEquals(contact.getMobileFirst(), deserializedContact.getMobileFirst());
  }

  @Test
  void testContactsCrescendoDeserialization() throws JsonProcessingException {
    // Arrange
    String json =
        """
        {
          "Id": "003XXXXXXXXXXXXXXX",
          "AccountId": "001XXXXXXXXXXXXXXX",
          "LastName": "Doe",
          "FirstName": "John",
          "Phone": "************",
          "External_Id__c": "ext-123",
          "Mobile_First__c": true
        }
        """;

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    ContactsCrescendo contact = objectMapper.readValue(json, ContactsCrescendo.class);

    // Assert
    assertNotNull(contact);
    assertEquals("003XXXXXXXXXXXXXXX", contact.getId());
    assertEquals("001XXXXXXXXXXXXXXX", contact.getAccountId());
    assertEquals("Doe", contact.getLastName());
    assertEquals("John", contact.getFirstName());
    assertEquals("************", contact.getPhone());
    assertEquals("ext-123", contact.getExternalId());
    assertEquals(true, contact.getMobileFirst());
  }

  @Test
  void testContactsCrescendoWithNullValues() throws JsonProcessingException {
    // Arrange
    ContactsCrescendo contact = new ContactsCrescendo();
    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    String json = objectMapper.writeValueAsString(contact);
    ContactsCrescendo deserializedContact = objectMapper.readValue(json, ContactsCrescendo.class);

    // Assert
    assertNotNull(json);
    assertNotNull(deserializedContact);
    assertNull(deserializedContact.getId());
    assertNull(deserializedContact.getAccountId());
    assertNull(deserializedContact.getLastName());
    assertNull(deserializedContact.getFirstName());
    assertNull(deserializedContact.getPhone());
    assertNull(deserializedContact.getExternalId());
    assertNull(deserializedContact.getMobileFirst());
  }

  @Test
  void testContactsCrescendoWithFalseMobileFirst() throws JsonProcessingException {
    // Arrange
    ContactsCrescendo contact = new ContactsCrescendo();
    contact.setId("003XXXXXXXXXXXXXXX");
    contact.setMobileFirst(false);

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    String json = objectMapper.writeValueAsString(contact);
    ContactsCrescendo deserializedContact = objectMapper.readValue(json, ContactsCrescendo.class);

    // Assert
    assertNotNull(json);
    assertTrue(json.contains("\"Mobile_First__c\":false"));
    assertEquals(false, deserializedContact.getMobileFirst());
  }

  @Test
  void testContactsCrescendoFieldComparison() {
    // Arrange - Test that objects with same field values have same field values
    ContactsCrescendo contact1 = new ContactsCrescendo();
    contact1.setId("003XXXXXXXXXXXXXXX");
    contact1.setFirstName("John");
    contact1.setLastName("Doe");
    contact1.setAccountId("001XXXXXXXXXXXXXXX");
    contact1.setPhone("************");
    contact1.setExternalId("ext-123");
    contact1.setMobileFirst(true);

    ContactsCrescendo contact2 = new ContactsCrescendo();
    contact2.setId("003XXXXXXXXXXXXXXX");
    contact2.setFirstName("John");
    contact2.setLastName("Doe");
    contact2.setAccountId("001XXXXXXXXXXXXXXX");
    contact2.setPhone("************");
    contact2.setExternalId("ext-123");
    contact2.setMobileFirst(true);

    ContactsCrescendo contact3 = new ContactsCrescendo();
    contact3.setId("004XXXXXXXXXXXXXXX");
    contact3.setFirstName("Jane");
    contact3.setLastName("Smith");
    contact3.setAccountId("002XXXXXXXXXXXXXXX");
    contact3.setPhone("************");
    contact3.setExternalId("ext-456");
    contact3.setMobileFirst(false);

    // Assert - Test field values are correctly set and retrieved
    assertEquals(contact1.getId(), contact2.getId());
    assertEquals(contact1.getFirstName(), contact2.getFirstName());
    assertEquals(contact1.getLastName(), contact2.getLastName());
    assertEquals(contact1.getAccountId(), contact2.getAccountId());
    assertEquals(contact1.getPhone(), contact2.getPhone());
    assertEquals(contact1.getExternalId(), contact2.getExternalId());
    assertEquals(contact1.getMobileFirst(), contact2.getMobileFirst());

    assertNotEquals(contact1.getId(), contact3.getId());
    assertNotEquals(contact1.getFirstName(), contact3.getFirstName());
    assertNotEquals(contact1.getLastName(), contact3.getLastName());
    assertNotEquals(contact1.getMobileFirst(), contact3.getMobileFirst());

    // Test that objects are not null and have different references
    assertNotNull(contact1);
    assertNotNull(contact2);
    assertNotSame(contact1, contact2);
  }

  @Test
  void testContactsCrescendoToString() {
    // Arrange
    ContactsCrescendo contact = new ContactsCrescendo();
    contact.setId("003XXXXXXXXXXXXXXX");
    contact.setFirstName("John");
    contact.setLastName("Doe");
    contact.setPhone("************");

    // Act
    String toString = contact.toString();

    // Assert
    assertNotNull(toString);
    assertTrue(toString.contains("ContactsCrescendo"));
    assertTrue(toString.contains("003XXXXXXXXXXXXXXX"));
    assertTrue(toString.contains("John"));
    assertTrue(toString.contains("Doe"));
    assertTrue(toString.contains("************"));
  }

  @Test
  void testContactsCrescendoConstructors() {
    // Test no-args constructor
    ContactsCrescendo contact1 = new ContactsCrescendo();
    assertNotNull(contact1);
    assertNull(contact1.getId());

    // Test all-args constructor
    ContactsCrescendo contact2 =
        new ContactsCrescendo(
            "003XXXXXXXXXXXXXXX",
            "001XXXXXXXXXXXXXXX",
            "Doe",
            "John",
            "************",
            "ext-123",
            true,
            "<EMAIL>");
    assertNotNull(contact2);
    assertEquals("003XXXXXXXXXXXXXXX", contact2.getId());
    assertEquals("001XXXXXXXXXXXXXXX", contact2.getAccountId());
    assertEquals("Doe", contact2.getLastName());
    assertEquals("John", contact2.getFirstName());
    assertEquals("************", contact2.getPhone());
    assertEquals("ext-123", contact2.getExternalId());
    assertEquals(true, contact2.getMobileFirst());
  }

  @Test
  void testContactsCrescendoIgnoreUnknownProperties() throws JsonProcessingException {
    // Arrange
    String jsonWithUnknownProperty =
        """
        {
          "Id": "003XXXXXXXXXXXXXXX",
          "FirstName": "John",
          "LastName": "Doe",
          "UnknownProperty": "SomeValue",
          "AnotherUnknownField": 123
        }
        """;

    ObjectMapper objectMapper = new ObjectMapper();

    // Act & Assert - Should not throw exception due to @JsonIgnoreProperties(ignoreUnknown = true)
    assertDoesNotThrow(
        () -> {
          ContactsCrescendo contact =
              objectMapper.readValue(jsonWithUnknownProperty, ContactsCrescendo.class);
          assertEquals("003XXXXXXXXXXXXXXX", contact.getId());
          assertEquals("John", contact.getFirstName());
          assertEquals("Doe", contact.getLastName());
        });
  }

  @Test
  void testContactsCrescendoJsonPropertyMapping() throws JsonProcessingException {
    // Arrange - Test that JSON property names are correctly mapped
    String json =
        """
        {
          "Id": "003XXXXXXXXXXXXXXX",
          "AccountId": "001XXXXXXXXXXXXXXX",
          "LastName": "Doe",
          "FirstName": "John",
          "Phone": "************",
          "External_Id__c": "ext-123",
          "Mobile_First__c": true
        }
        """;

    ObjectMapper objectMapper = new ObjectMapper();

    // Act
    ContactsCrescendo contact = objectMapper.readValue(json, ContactsCrescendo.class);

    // Assert - Verify all JSON properties are correctly mapped to Java fields
    assertEquals("003XXXXXXXXXXXXXXX", contact.getId());
    assertEquals("001XXXXXXXXXXXXXXX", contact.getAccountId());
    assertEquals("Doe", contact.getLastName());
    assertEquals("John", contact.getFirstName());
    assertEquals("************", contact.getPhone());
    assertEquals("ext-123", contact.getExternalId());
    assertEquals(true, contact.getMobileFirst());

    // Test serialization back to JSON
    String serializedJson = objectMapper.writeValueAsString(contact);
    assertTrue(serializedJson.contains("\"External_Id__c\":\"ext-123\""));
    assertTrue(serializedJson.contains("\"Mobile_First__c\":true"));
  }

  @Test
  void testContactsCrescendoSettersAndGetters() {
    // Arrange
    ContactsCrescendo contact = new ContactsCrescendo();

    // Act & Assert - Test all setters and getters
    contact.setId("003XXXXXXXXXXXXXXX");
    assertEquals("003XXXXXXXXXXXXXXX", contact.getId());

    contact.setAccountId("001XXXXXXXXXXXXXXX");
    assertEquals("001XXXXXXXXXXXXXXX", contact.getAccountId());

    contact.setLastName("Doe");
    assertEquals("Doe", contact.getLastName());

    contact.setFirstName("John");
    assertEquals("John", contact.getFirstName());

    contact.setPhone("************");
    assertEquals("************", contact.getPhone());

    contact.setExternalId("ext-123");
    assertEquals("ext-123", contact.getExternalId());

    contact.setMobileFirst(true);
    assertEquals(true, contact.getMobileFirst());

    contact.setMobileFirst(false);
    assertEquals(false, contact.getMobileFirst());
  }
}
