/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.app.cargill.constants.*;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import lombok.*;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode(callSuper = true)
public class DefaultAppSettingDocument extends EditableDocumentBase implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonProperty("CountryId")
  private Business countryId;

  @JsonProperty("UnitOfMeasure")
  private UnitOfMeasureKeys unitOfMeasure;

  @JsonProperty("BCSPointScale")
  private BCSPointScale bcsPointScale;

  @JsonProperty("DefaultCurrency")
  private Currencies defaultCurrency;

  @Builder.Default private Map<String, String> defaultValues = new HashMap<>();
}
