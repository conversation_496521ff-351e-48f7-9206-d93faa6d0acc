/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum ReportTypeCrescendo {
  SITE_VISIT("SiteVisit"),
  WALKTHROUGH("WalkThrough"),
  TOOL("Tool");

  private final String value;

  @JsonCreator
  ReportTypeCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }
}
