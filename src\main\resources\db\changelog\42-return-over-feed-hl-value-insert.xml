<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="042" author="Taha">
	 <sql splitStatements="false">
    
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'c5f38afd-5406-44a8-942f-89af8c99acd2', 'Price', null, 'Value', 4.08, 'Name', 'butterfat', 'ReturnOverFeedType', 'PERCENTAGE_KG_PER_HL'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '42ae3e9a-3d09-4675-85fe-eac502338975', 'Price', null, 'Value', 3.24, 'Name', 'protein', 'ReturnOverFeedType', 'PERCENTAGE_KG_PER_HL'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', '1706a0bb-e786-42e6-ae0f-4f89028939fb', 'Price', null, 'Value', 5.97, 'Name', 'lactoseAndOtherSolids', 'ReturnOverFeedType', 'PERCENTAGE_KG_PER_HL'));
INSERT INTO return_over_feed_pricings (created_date, deleted, local_id, updated_date, pricing_return_over_feed_document) VALUES (CURRENT_TIMESTAMP, false, NULL, CURRENT_TIMESTAMP, jsonb_build_object('id', 'bb443242-7b23-4cb9-b786-e595f3bfe63b', 'Price', null, 'Value', 2.2, 'Name', 'maxAllowed', 'ReturnOverFeedType', 'OTHERS'));


</sql>


	</changeSet>

</databaseChangeLog>