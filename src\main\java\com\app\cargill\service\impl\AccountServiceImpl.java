/* Cargill Inc.(C) 2022 */
package com.app.cargill.service.impl;

import com.app.cargill.constants.Business;
import com.app.cargill.constants.LangKeys;
import com.app.cargill.constants.ResponseStatus;
import com.app.cargill.constants.SubTypeId;
import com.app.cargill.crescendo.constants.NineBoxStepTwoIDCrescendo;
import com.app.cargill.crescendo.model.AccountTeamMember;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoAccountService;
import com.app.cargill.crescendo.service.ICrescendoAccountTeamMemberService;
import com.app.cargill.crescendo.service.ICrescendoContactService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.document.Address;
import com.app.cargill.document.Contact;
import com.app.cargill.document.DataSource;
import com.app.cargill.document.DateEpoch;
import com.app.cargill.document.UserRole;
import com.app.cargill.dto.AccountDto;
import com.app.cargill.dto.AccountFavouriteDto;
import com.app.cargill.dto.ContactDto;
import com.app.cargill.dto.LiftResponseEntityDto;
import com.app.cargill.dto.PayloadValidationDto;
import com.app.cargill.exceptions.AlreadyExistsDEException;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.exceptions.NotFoundDEException;
import com.app.cargill.model.Accounts;
import com.app.cargill.repository.AccountsRepository;
import com.app.cargill.repository.SitesRepository;
import com.app.cargill.repository.UserRepository;
import com.app.cargill.salesforce.errors.LiftErrorResponseConstants;
import com.app.cargill.service.IAccountService;
import com.app.cargill.service.IS3Service;
import com.app.cargill.service.IUserService;
import com.app.cargill.service.impl.mappers.AccountMapper;
import com.app.cargill.sf.cc.constants.LiftEntityName;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.sf.cc.model.simple.AccountUpdateModel;
import com.app.cargill.sf.cc.model.simple.MobileToLiftContactSalesforce;
import com.app.cargill.sf.cc.model.simple.User;
import com.app.cargill.sf.cc.service.LiftAccountService;
import com.app.cargill.sf.cc.service.LiftApiService;
import com.app.cargill.sf.cc.service.LiftContactService;
import com.app.cargill.sf.cc.service.LiftUserAccessService;
import com.app.cargill.sf.cc.service.LiftUserService;
import com.app.cargill.sf.cc.utils.LiftUtils;
import com.app.cargill.utils.AccountTypeMapper;
import com.app.cargill.utils.JsonUtils;
import com.app.cargill.utils.PageableUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.gson.JsonObject;
import com.nimbusds.jose.shaded.gson.Gson;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service("accountServiceImpl")
@RequiredArgsConstructor
@SuppressWarnings({"java:S125"})
public class AccountServiceImpl implements IAccountService {

  private final AccountsRepository accountsRepository;
  private final IS3Service s3ServiceImpl;

  private final LiftUserAccessService liftUserAccessService;
  private final LiftUtils liftUtils;

  private final SitesRepository sitesRepository;

  private final IUserService userServiceImpl;

  private final UserRepository userRepository;

  private final ICrescendoAccountService crescendoAccountService;
  private final ICrescendoContactService crescendoContactService;
  private final ICrescendoAccountTeamMemberService accountTeamMemberService;
  private final ICrescendoUserService crescendoUserService;

  private final LiftAccountService liftAccountService;
  private final LiftContactService liftContactService;
  private final LiftUserService liftUserService;
  private final LiftApiService liftApiService;
  private static final String ACCOUNT = "Account";
  private static final String CONTACT = "Contact";
  private static final String ACCOUNT_REPRESENTATIVE = "Account Representative";
  private static final String TECHNICAL_SPECIALIST = "Technical Specialist";
  private static final String DAIRY_FOCUS_CONSULTANT = "Dairy Focus Consultant";
  private static final String CHATTER_FREE = "Chatter Free";

  @Override
  public List<AccountDto> getAllAccounts(String accountType, Instant syncTime, String email) {

    List<Accounts> accounts;
    List<String> users = new ArrayList<>();
    users.add(email);
    String usersJson = new Gson().toJson(users);
    if (!accountType.isBlank()) {
      accounts =
          accountsRepository.findAllBySubtypeAccountTypeSyncTimeAndAccountStatus(
              SubTypeId.FarmProducer.name(), accountType, syncTime, usersJson);
    } else {
      accounts =
          accountsRepository.findAllBySubTypeSyncTimeAndAccountStatus(
              SubTypeId.FarmProducer.name(), syncTime, usersJson);
    }

    return accounts.stream().map(AccountMapper::mapToDto).toList();
  }

  @Override
  public Page<AccountDto> getPaginatedAccounts(
      String search,
      int page,
      int size,
      String sortBy,
      String accountType,
      Instant syncTime,
      String email,
      String sorting) {
    Pageable pageable = PageableUtil.getPageable(page, size, sortBy, sorting);
    Page<Accounts> accountsList = null;
    String usersJson = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    String userEmail = userServiceImpl.getCurrentLoggedInUser();
    JsonObject userRolesJson = new JsonObject();
    userRolesJson.addProperty("UserName", userEmail);
    userRolesJson.addProperty("UserRole", TECHNICAL_SPECIALIST);
    if (!search.isBlank()) {
      if (!accountType.isBlank()) {
        accountsList =
            accountsRepository
                .findAllAccountsBySearchItemSubTypeIdAccountIdSyncTimeAndAccountStatusPaginated(
                    search,
                    SubTypeId.FarmProducer.name(),
                    accountType,
                    syncTime,
                    usersJson,
                    "[" + userRolesJson.toString() + "]",
                    pageable);
      } else {
        accountsList =
            accountsRepository.findAllAccountsBySearchItemSubTypeSyncTimeAndAccountStatusPaginated(
                search,
                SubTypeId.FarmProducer.name(),
                syncTime,
                usersJson,
                "[" + userRolesJson.toString() + "]",
                pageable);
      }
    } else {
      if (!accountType.isBlank()) {
        accountsList =
            accountsRepository.findAllBySubTypeIdAccountTypeSyncTimeAndAccountStatusPaginated(
                SubTypeId.FarmProducer.name(),
                syncTime,
                accountType,
                usersJson,
                userEmail,
                pageable);
      } else {
        accountsList =
            accountsRepository.findAllBySubTypeIdSyncTimeAndAccountStatusPaginated(
                SubTypeId.FarmProducer.name(), syncTime, usersJson, userEmail, pageable);
      }
    }
    if (Objects.isNull(accountsList)) {
      return new PageImpl<>(new ArrayList<>());
    }
    return new PageImpl<>(
        accountsList.stream().map(AccountMapper::mapToDto).toList(),
        pageable,
        accountsList.getTotalElements());
  }

  @Override
  public List<String> getFilteredAccountIds() {
    String usersJson = userServiceImpl.getCurrentLoggedInUserAsJsonObj();
    String userEmail = userServiceImpl.getCurrentLoggedInUser();
    JsonObject userRolesJson = new JsonObject();
    userRolesJson.addProperty("UserName", userEmail);
    userRolesJson.addProperty("UserRole", TECHNICAL_SPECIALIST);

    List<String> filteredAccountIds =
        accountsRepository.findAllFilteredAccountIds(
            SubTypeId.FarmProducer.name(), usersJson, userEmail);

    if (Objects.isNull(filteredAccountIds)) {
      return new ArrayList<>();
    }

    return filteredAccountIds;
  }

  @Override
  public AccountDto save(AccountDto accountDto, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    UUID accountUuid = UUID.randomUUID();
    UUID contactsUuid = UUID.randomUUID();
    String imageUrl = accountDto.getPhotoId();
    if (accountDto.getLocalId() == null) {
      log.error("localId is null");
      throw new NotFoundDEException("localID is NULL");
    }
    if (accountsRepository.existsByLocalId(accountDto.getLocalId())) {
      throw new AlreadyExistsDEException("LocalID Already Exists. Account Already Synced.");
    }
    try {
      if (!Objects.isNull(accountDto.getImageToUploadBase64())
          && !StringUtils.isBlank(accountDto.getImageToUploadBase64())) {

        imageUrl =
            s3ServiceImpl.uploadFile(
                Base64.getDecoder().decode(accountDto.getImageToUploadBase64()),
                "Prospect-" + accountUuid,
                false);
      }
    } catch (Exception e) {
      log.error("Unable to upload image to S3 ", e);
    }

    Accounts account = mapToModel(accountDto, accountUuid, contactsUuid, imageUrl);

    String currentLoggedInUser = userServiceImpl.getCurrentLoggedInUser();

    // Crescendo setup from Mobile side.Owner id = user email, NineBoxStepTwoID i have no clue about
    // hence setting it as Not_defined at this point.
    // Users array is getting populated correctly, Source = Crescendo in case of countries other
    // than Global, Brazil, Canada and US
    // BusinessId is basically the country Id. that is being set up properly.
    if (DataSource.CRESCENDO.equals(account.getAccountDocument().getDataSource())) {
      if (account.getAccountDocument().getSegmentStepOneId() != null
          && account.getAccountDocument().getSegmentStepOneId().equalsIgnoreCase("End-User")) {
        account.getAccountDocument().setSegmentStepOneId("End User");
      }

      account.getAccountDocument().setOwnerId(currentLoggedInUser);
      account
          .getAccountDocument()
          .setNineBoxStepTwoID(NineBoxStepTwoIDCrescendo.NOT_DEFINED.getValue());
      account.getAccountDocument().setNeedsSync(true);
      List<UserRole> userRoles = createCrescendoUserRole();
      account.getAccountDocument().setUserRoles(userRoles);
      saveToCrescendo(account, locale, source);
    }

    if (DataSource.LIFT.equals(account.getAccountDocument().getDataSource())) {
      saveToLift(account, currentLoggedInUser, locale, source);
    }

    return AccountMapper.mapToDto(accountsRepository.saveAndFlush(account));
  }

  private void saveToCrescendo(Accounts account, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException {

    account
        .getAccountDocument()
        .setGoldenRecordId(
            crescendoAccountService.createAccount(account.getAccountDocument(), locale, source));

    if (account.getAccountDocument().getContacts() != null
        && !account.getAccountDocument().getContacts().isEmpty()) {

      account.getAccountDocument().getContacts().stream()
          .forEach(
              contact -> {
                contact.setGoldenRecordAcountId(account.getAccountDocument().getGoldenRecordId());
                try {
                  contact.setSFDCContactId(
                      crescendoContactService.createContact(contact, locale, source));
                } catch (CustomDEExceptions e) {
                  log.error(e.getLocalizedMessage());
                }
              });
    }
    if (account.getAccountDocument().getUserRoles() != null
        && !account.getAccountDocument().getUserRoles().isEmpty()) {

      AccountTeamMember accountTeamMember = documentToCrescendoAccountTeamMember(account);
      accountTeamMemberService.createAccountTeamMember(accountTeamMember, locale, source);
    }
  }

  private AccountTeamMember documentToCrescendoAccountTeamMember(Accounts account)
      throws CustomDEExceptions {

    SalesforceRecordsResponse<Users> user =
        crescendoUserService.getByUserEmail(account.getAccountDocument().getOwnerId());

    AccountTeamMember accountTeamMember = new AccountTeamMember();

    accountTeamMember.setAccountId(account.getAccountDocument().getGoldenRecordId());
    accountTeamMember.setAccountAccessLevel("Edit");
    accountTeamMember.setContactAccessLevel("Edit");
    accountTeamMember.setTeamMemberRole(DAIRY_FOCUS_CONSULTANT);
    accountTeamMember.setUserId(ACCOUNT);
    accountTeamMember.setUserId(user.getRecords().get(0).getId());

    return accountTeamMember;
  }

  public List<UserRole> createCrescendoUserRole() {
    List<UserRole> userRoles = new ArrayList<>();
    UserRole userRole =
        UserRole.builder()
            .userName(userServiceImpl.getCurrentLoggedInUser())
            .roleType(DAIRY_FOCUS_CONSULTANT)
            .build();
    userRoles.add(userRole);
    return userRoles;
  }

  Accounts saveToLift(
      Accounts account,
      String currentLoggedInUser,
      Locale locale,
      ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException, JsonProcessingException,
          CustomDEExceptions {
    log.debug("Checking for user: {}", currentLoggedInUser);

    User user = liftUserService.findOwner(currentLoggedInUser);

    if (user != null) {

      log.debug("SF USER: {}", user.getEmail());
      account.getAccountDocument().setOwnerId(user.getEmail());
      log.debug("VALIDATING ACCOUNT");
      AccountUpdateModel salesForceAccount =
          liftAccountService.documentToModel(account.getAccountDocument(), user.getId());
      PayloadValidationDto accountValidations =
          liftApiService.validate(salesForceAccount, LiftEntityName.ACCOUNT, locale, source);
      if (!accountValidations.getErrorDetails().isEmpty()) {
        accountValidations.getErrorDetails().forEach(validation -> validation.setEntity(ACCOUNT));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(accountValidations.getErrorDetails()));
      }

      log.debug("ACCOUNT VALIDATED");

      log.debug("VALIDATING CONTACT");
      MobileToLiftContactSalesforce salesForceContact =
          liftContactService.validateDocument(
              account.getAccountDocument().getContacts().isEmpty()
                  ? null
                  : account.getAccountDocument().getContacts().get(0));
      PayloadValidationDto contactValidations =
          liftApiService.validate(salesForceContact, LiftEntityName.CONTACT, locale, source);
      if (!contactValidations.getErrorDetails().isEmpty()) {
        contactValidations.getErrorDetails().forEach(validation -> validation.setEntity(CONTACT));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(contactValidations.getErrorDetails()));
      }
      log.debug("CONTACT VALIDATED");

      AccountDocument modifiedDocument =
          liftAccountService.createAccount(account.getAccountDocument(), locale, source);
      account.getAccountDocument().setGoldenRecordId(modifiedDocument.getGoldenRecordId());
      if (account.getAccountDocument().getContacts() != null
          && !account.getAccountDocument().getContacts().isEmpty()) {
        account
            .getAccountDocument()
            .getContacts()
            .forEach(
                contact ->
                    contact.setGoldenRecordAcountId(
                        account.getAccountDocument().getGoldenRecordId()));
        account
            .getAccountDocument()
            .setContacts(
                liftContactService.saveToLift(
                    account.getAccountDocument().getContacts(), locale, source));

        User liftUser = liftUserService.findUserByEmail(currentLoggedInUser);
        if (!Objects.isNull(liftUser)
            && liftUser.getProfile().getUserLicense().getName().equalsIgnoreCase(CHATTER_FREE)) {
          account.getAccountDocument().setUserRoles(createUserRoles(currentLoggedInUser));
          liftUserAccessService.createUserAccess(
              liftUser.getId(),
              account.getAccountDocument().getGoldenRecordId(),
              ACCOUNT_REPRESENTATIVE);
        }
      }
    } else {
      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(
                  source.getMessage(
                      LangKeys.NO_USER_FOUND,
                      new Object[] {},
                      LiftErrorResponseConstants.USER_NOT_FOUND,
                      locale))
              .entity(ACCOUNT)
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
          HttpStatus.SC_FORBIDDEN);
    }
    return account;
  }

  public List<UserRole> createUserRoles(String currentLoggedInUser) {
    UserRole userRole =
        UserRole.builder().roleType(ACCOUNT_REPRESENTATIVE).userName(currentLoggedInUser).build();
    List<UserRole> userRoles = new ArrayList<>();
    userRoles.add(userRole);
    return userRoles;
  }

  public Integer getActualSiteCount(Accounts account) {
    return sitesRepository.countByAccountId(account.getAccountDocument().getId().toString());
  }

  @Override
  public AccountDto update(AccountDto accountDto, Locale locale, ResourceBundleMessageSource source)
      throws JsonProcessingException, IllegalAccessException, ClassNotFoundException,
          CustomDEExceptions {
    String imageUrl = accountDto.getPhotoId();
    try {
      if (!Objects.isNull(accountDto.getImageToUploadBase64())
          && !StringUtils.isBlank(accountDto.getImageToUploadBase64())) {

        imageUrl =
            s3ServiceImpl.uploadFile(
                Base64.getDecoder().decode(accountDto.getImageToUploadBase64()),
                "Prospect-" + accountDto.getId().toString(),
                false);
      }
    } catch (Exception e) {
      // @TODO change message and logic
      log.error("Exception occurred", e);
    }
    Accounts account = dtoToUpdateModel(accountDto, imageUrl);

    if (DataSource.LIFT.equals(account.getAccountDocument().getDataSource())) {
      updateContactOnLift(account, locale, source);
    }
    if (DataSource.CRESCENDO.equals(account.getAccountDocument().getDataSource())) {
      createCrescendoUserRolesIfNotExist(account);
      updateToCrescendo(account, locale, source);
    }

    return AccountMapper.mapToDto(accountsRepository.save(account));
  }

  private void updateToCrescendo(
      Accounts account, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions {

    crescendoAccountService.updateAccount(account.getAccountDocument(), locale, source);

    Contact contact =
        !account.getAccountDocument().getContacts().isEmpty()
            ? account.getAccountDocument().getContacts().get(0)
            : null;

    if (Objects.nonNull(contact)) {

      crescendoContactService.updateContact(contact, locale, source);
    }

    boolean hasRole = false;

    if (account.getAccountDocument().getUserRoles() != null
        && !account.getAccountDocument().getUserRoles().isEmpty()) {

      Optional<UserRole> matchingRole =
          account.getAccountDocument().getUserRoles().stream()
              .filter(
                  userRole ->
                      userRole.getUserName().equals(userServiceImpl.getCurrentLoggedInUser()))
              .findFirst();

      hasRole = matchingRole.isPresent();

      if (!hasRole) {
        AccountTeamMember accountTeamMember = documentToCrescendoAccountTeamMember(account);
        accountTeamMemberService.createAccountTeamMember(accountTeamMember, locale, source);
      }
    }
  }

  public void createCrescendoUserRolesIfNotExist(Accounts account) {
    boolean isExist =
        account.getAccountDocument().getUserRoles().stream()
            .anyMatch(
                userRole ->
                    userRole
                        .getUserName()
                        .equalsIgnoreCase(userServiceImpl.getCurrentLoggedInUser()));
    if (!isExist) {
      UserRole userRole =
          UserRole.builder()
              .roleType(DAIRY_FOCUS_CONSULTANT)
              .userName(userServiceImpl.getCurrentLoggedInUser())
              .build();
      List<UserRole> userRoles = new ArrayList<>(account.getAccountDocument().getUserRoles());
      userRoles.add(userRole);
      account.getAccountDocument().setUserRoles(userRoles);
    }
  }

  public void updateContactOnLift(
      Accounts account, Locale locale, ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException, JsonProcessingException,
          CustomDEExceptions {
    User user = liftUserService.findOwner(userServiceImpl.getCurrentLoggedInUser());
    if (user != null && account.getAccountDocument().getGoldenRecordId() != null) {
      AccountUpdateModel salesForceAccount =
          liftAccountService.documentToModel(account.getAccountDocument(), user.getId());
      PayloadValidationDto accountValidations =
          liftApiService.validate(salesForceAccount, LiftEntityName.ACCOUNT, locale, source);
      if (!accountValidations.getErrorDetails().isEmpty()) {
        accountValidations.getErrorDetails().forEach(validation -> validation.setEntity(ACCOUNT));
        throw new CustomDEExceptions(
            JsonUtils.toJsonWithoutPrettyPrinter(accountValidations.getErrorDetails()));
      }

      log.debug("ACCOUNT VALIDATED");
      Contact contact =
          !account.getAccountDocument().getContacts().isEmpty()
              ? account.getAccountDocument().getContacts().get(0)
              : null;
      createContactAndUserAccessIfNotExists(account, contact, locale, source);
    } else {
      String langKey = liftUtils.getLangKey(user, account, null);
      String defaultValue = liftUtils.getDefaultValue(user, account, null);

      PayloadValidationDto payloadValidationDto = new PayloadValidationDto();
      LiftResponseEntityDto liftResponseEntityDto =
          LiftResponseEntityDto.builder()
              .message(source.getMessage(langKey, new Object[] {}, defaultValue, locale))
              .entity(ACCOUNT)
              .status(ResponseStatus.FAILED)
              .build();
      payloadValidationDto.getErrorDetails().add(liftResponseEntityDto);
      throw new CustomDEExceptions(
          JsonUtils.toJsonWithoutPrettyPrinter(payloadValidationDto.getErrorDetails()),
          HttpStatus.SC_FORBIDDEN);
    }
  }

  private void createContactAndUserAccessIfNotExists(
      Accounts account, Contact contact, Locale locale, ResourceBundleMessageSource source)
      throws IllegalAccessException, ClassNotFoundException, CustomDEExceptions,
          JsonProcessingException {
    if (!Objects.isNull(contact)) {
      log.debug("VALIDATING CONTACT FOR SF");
      MobileToLiftContactSalesforce contactForSalesforce =
          liftContactService.validateDocument(contact);
      PayloadValidationDto contactValidation =
          liftApiService.validate(contactForSalesforce, LiftEntityName.CONTACT, locale, source);
      if (!contactValidation.getErrorDetails().isEmpty()) {
        contactValidation.getErrorDetails().forEach(validation -> validation.setEntity(CONTACT));
        throw new CustomDEExceptions(JsonUtils.toJson(contactValidation.getErrorDetails()));
      }
      log.debug("CONTACT VALIDATED");
      if (contact.getSFDCContactId() == null) {
        contact.setSFDCContactId(liftContactService.createContact(contact, locale, source));

        account
            .getAccountDocument()
            .getContacts()
            .forEach(
                contactInDb -> {
                  if (contactInDb
                      .getContactId()
                      .toString()
                      .equals(contact.getContactId().toString())) {
                    contactInDb.setSFDCContactId(contact.getSFDCContactId());
                  }
                });
      } else {
        liftContactService.updateContact(contact, source, locale);
      }
    }
    createUserRoleIfNotExist(account);
  }

  public void createUserRoleIfNotExist(Accounts account) {
    User user = liftUserService.findUserByEmail(userServiceImpl.getCurrentLoggedInUser());
    if (!Objects.isNull(user)
        && user.getProfile().getUserLicense().getName().equalsIgnoreCase(CHATTER_FREE)) {
      if (account.getAccountDocument().getUserRoles() != null
          && !account.getAccountDocument().getUserRoles().isEmpty()) {
        boolean isExist =
            account.getAccountDocument().getUserRoles().stream()
                .anyMatch(
                    userRole ->
                        userRole
                            .getUserName()
                            .equalsIgnoreCase(userServiceImpl.getCurrentLoggedInUser()));
        if (!isExist) {
          UserRole userRole =
              UserRole.builder()
                  .roleType(ACCOUNT_REPRESENTATIVE)
                  .userName(userServiceImpl.getCurrentLoggedInUser())
                  .build();
          List<UserRole> userRoles = new ArrayList<>(account.getAccountDocument().getUserRoles());
          userRoles.add(userRole);
          account.getAccountDocument().setUserRoles(userRoles);
          liftUserAccessService.createUserAccess(
              user.getId(),
              account.getAccountDocument().getGoldenRecordId(),
              ACCOUNT_REPRESENTATIVE);
        }
      } else {
        account
            .getAccountDocument()
            .setUserRoles(createUserRoles(userServiceImpl.getCurrentLoggedInUser()));
        liftUserAccessService.createUserAccess(
            user.getId(), account.getAccountDocument().getGoldenRecordId(), ACCOUNT_REPRESENTATIVE);
      }
    }
  }

  @Override
  public AccountDto getAccountById(String id) {

    Accounts account = accountsRepository.findByAccountId(id);
    return AccountMapper.mapToDto(account);
  }

  @Override
  public AccountDto favourite(AccountFavouriteDto accountFavouriteDto) {
    Accounts account = accountsRepository.findByAccountId(accountFavouriteDto.getId().toString());
    if (account == null) {
      return null;
    }
    account.getAccountDocument().setFavourite(accountFavouriteDto.isFavourite());
    account = accountsRepository.save(account);
    return AccountMapper.mapToDto(account);
  }

  @Override
  public List<AccountDto> getAllAccountsNonPaginated() {

    List<Accounts> accounts = accountsRepository.findAll();

    return accounts.stream().map(AccountMapper::mapToDto).toList();
  }

  public List<Accounts> getAllUnsyncedAccounts(DataSource dataSource) {
    return accountsRepository.findAllByAccountDocumentNeedsSyncAndAccountDocumentDataSource(
        "true", dataSource.toString());
  }

  private Accounts mapToModel(
      AccountDto accountDto, UUID accountUuid, UUID contactsUuid, String imageUrl) {
    List<String> users = new ArrayList<>();
    if (accountDto.getUsers() != null) {
      users.addAll(accountDto.getUsers());
    }

    String countryId =
        userRepository.findCountryIdByUserName(userServiceImpl.getCurrentLoggedInUser());
    AccountDocument accountsDocument =
        AccountDocument.builder()
            .id(accountUuid)
            .active(true)
            .accountName(accountDto.getBusinessName())
            .customerCode(accountDto.getCustomerCode())
            .accountType(accountDto.getAccountType())
            .segmentStepOneId(accountDto.getSegmentId())
            .dateOfLastVisit(accountDto.getDateOfLastVisit())
            .accountStatus("New")
            .businessID(calculateBusinessId(countryId))
            .isFavourite(accountDto.isFavourite())
            .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
            .subTypeID(accountDto.getType() != null ? SubTypeId.fromId(accountDto.getType()) : null)
            .users(new HashSet<>(users))
            .build();
    accountsDocument.setDataSource(findDataSource(countryId));
    accountsDocument.setNeedsSync(false);

    accountsDocument.setType(AccountTypeMapper.map(accountDto.getAccountType()).getValue());

    List<Contact> contacts = new ArrayList<>();

    if (accountDto.getContacts() != null && !accountDto.getContacts().isEmpty()) {

      for (ContactDto contactDto : accountDto.getContacts()) {

        UUID contactUuid = UUID.randomUUID();

        Date recentDate = new Date();
        DateEpoch epochDate = new DateEpoch();
        epochDate.setDate(Instant.now());
        epochDate.setEpoch(epochDate.getEpoch());
        Contact contact =
            Contact.builder()
                .accountId(accountUuid)
                .contactId(contactUuid)
                .createTimeUtc(recentDate.toInstant())
                .lastModifiedTimeUtc(epochDate)
                .needsSync(true)
                .createUser(userServiceImpl.getCurrentLoggedInUser())
                .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
                .lastSyncTimeUtc(recentDate.toInstant())
                .firstName(contactDto.getFirstName())
                .lastName(contactDto.getLastName())
                .lastUpdateDateTime(epochDate)
                .emailAddress(contactDto.getEmail())
                .phoneNumber(contactDto.getPhoneNumber())
                .build();

        contacts.add(contact);
      }

      accountsDocument.setPrimaryContactId(contactsUuid);
      accountsDocument.setPrimaryContactTitle(
          accountDto.getContacts().get(0) != null
              ? accountDto.getContacts().get(0).getFirstName()
                  + " "
                  + accountDto.getContacts().get(0).getLastName()
              : null);
      accountsDocument.setPrimaryContactPhoneNumber(
          accountDto.getContacts().get(0).getPhoneNumber() != null
              ? accountDto.getContacts().get(0).getPhoneNumber()
              : null);
    }

    accountsDocument.setContacts(contacts);
    if (accountDto.getPhysicalAddress() != null) {
      Address address =
          Address.builder()
              .city(accountDto.getPhysicalAddress().getCity())
              .country(accountDto.getCountry())
              .street(accountDto.getPhysicalAddress().getStreet())
              .postalCode(accountDto.getPhysicalAddress().getPostalCode())
              .stateOrProvince(accountDto.getPhysicalAddress().getState())
              .build();

      accountsDocument.setPhysicalAddress(address);
    }
    accountsDocument.setPhotoId(imageUrl);

    accountsDocument.setLastSyncTimeUtc(Instant.now());
    accountsDocument.setCreateTimeUtc(Instant.now());
    accountsDocument.setActive(true);
    return Accounts.builder()
        .accountDocument(accountsDocument)
        .localId(accountDto.getLocalId())
        .build();
  }

  public Integer calculateBusinessId(String countryId) {
    if (countryId == null) {
      return null;
    }

    if ("Kazakhstan".equals(countryId)) {
      return Business.valueOf("Russia").getBusinessId();
    }
    if ("Mexico".equals(countryId)) {
      return Business.valueOf("US").getBusinessId();
    }

    return Business.valueOf(countryId).getBusinessId();
  }

  private Accounts getExistingAccount(AccountDto accountDto) {
    if (accountDto.getId() == null) {
      throw new NotFoundDEException("Account Id is null");
    }
    Accounts existingAccount = accountsRepository.findByAccountId(accountDto.getId().toString());

    if (existingAccount == null) {
      throw new NotFoundDEException(
          "Account does not exist against ID: " + accountDto.getId().toString());
    }
    return existingAccount;
  }

  private Accounts dtoToUpdateModel(AccountDto accountDto, String imageUrl) {

    Accounts existingAccount = getExistingAccount(accountDto);

    if (existingAccount.getAccountDocument() == null) {

      UUID accountUuid = UUID.randomUUID();
      UUID contactsUuid = UUID.randomUUID();
      long id = existingAccount.getId();
      existingAccount = mapToModel(accountDto, accountUuid, contactsUuid, imageUrl);
      existingAccount.setId(id);
      accountsRepository.saveAndFlush(existingAccount);

      return existingAccount;

    } else {

      existingAccount.getAccountDocument().setAccountName(accountDto.getBusinessName());
      existingAccount.getAccountDocument().setCustomerCode(accountDto.getCustomerCode());
      existingAccount.getAccountDocument().setSegmentStepOneId(accountDto.getSegmentId());
      existingAccount.getAccountDocument().setAccountType(accountDto.getAccountType());
      existingAccount.getAccountDocument().setDateOfLastVisit(accountDto.getDateOfLastVisit());
      existingAccount.getAccountDocument().setActive(true);
      existingAccount
          .getAccountDocument()
          .setSubTypeID(
              accountDto.getType() != null ? SubTypeId.fromId(accountDto.getType()) : null);
      existingAccount
          .getAccountDocument()
          .setType(AccountTypeMapper.map(accountDto.getAccountType()).getValue());

      if (!CollectionUtils.isEmpty(accountDto.getContacts())) {
        populateContacts(accountDto, existingAccount);
      }

      if (accountDto.getPhysicalAddress() != null) {

        Address address =
            Address.builder()
                .city(accountDto.getPhysicalAddress().getCity())
                .postalCode(accountDto.getPhysicalAddress().getPostalCode())
                .street(accountDto.getPhysicalAddress().getStreet())
                .country(accountDto.getCountry())
                .stateOrProvince(accountDto.getPhysicalAddress().getState())
                .build();

        existingAccount.getAccountDocument().setPhysicalAddress(address);
      }
      if (imageUrl != null) {
        existingAccount.getAccountDocument().setPhotoId(imageUrl);
      }
      existingAccount
          .getAccountDocument()
          .setPrimaryContactTitle(
              !accountDto.getContacts().isEmpty() && accountDto.getContacts().get(0) != null
                  ? accountDto.getContacts().get(0).getFirstName()
                      + " "
                      + accountDto.getContacts().get(0).getLastName()
                  : null);
      existingAccount
          .getAccountDocument()
          .setPrimaryContactPhoneNumber(
              !CollectionUtils.isEmpty(accountDto.getContacts())
                  ? accountDto.getContacts().get(0).getPhoneNumber()
                  : "");
    }
    existingAccount.getAccountDocument().setActive(true);
    existingAccount.getAccountDocument().setLastSyncTimeUtc(Instant.now());
    existingAccount
        .getAccountDocument()
        .setLastModifyUser(userServiceImpl.getCurrentLoggedInUser());
    existingAccount.getAccountDocument().setNeedsSync(true);
    existingAccount.getAccountDocument().setNew(false);
    existingAccount.setLocalId(accountDto.getLocalId());
    existingAccount.getAccountDocument().setFavourite(accountDto.isFavourite());
    existingAccount.getAccountDocument().setSiteCount(getActualSiteCount(existingAccount));

    return existingAccount;
  }

  private void populateContacts(AccountDto accountDto, Accounts existingAccount) {

    if (Objects.isNull(existingAccount.getAccountDocument().getContacts())
        || existingAccount.getAccountDocument().getContacts().isEmpty()) {
      List<Contact> contacts = new ArrayList<>();
      accountDto
          .getContacts()
          .forEach(
              contactDto ->
                  contacts.add(
                      Contact.builder()
                          .accountId(accountDto.getId())
                          .contactId(UUID.randomUUID())
                          .firstName(contactDto.getFirstName())
                          .lastName(contactDto.getLastName())
                          .phoneNumber(contactDto.getPhoneNumber())
                          .emailAddress(contactDto.getEmail())
                          .goldenRecordAcountId(
                              existingAccount.getAccountDocument().getGoldenRecordId())
                          .needsSync(true)
                          .lastModifiedTimeUtc(new DateEpoch(Instant.now()))
                          .lastUpdateDateTime(new DateEpoch(Instant.now()))
                          .createTimeUtc(Instant.now())
                          .lastModifyUser(userServiceImpl.getCurrentLoggedInUser())
                          .createUser(userServiceImpl.getCurrentLoggedInUser())
                          .build()));
      existingAccount.getAccountDocument().setContacts(contacts);
    } else {
      existingAccount
          .getAccountDocument()
          .getContacts()
          .forEach(
              contact -> {
                ContactDto contactDto =
                    accountDto.getContacts().stream()
                        .filter(x -> contact.getContactId().equals(x.getContactId()))
                        .findFirst()
                        .orElse(null);

                if (contactDto != null) {
                  contact.setGoldenRecordAcountId(
                      existingAccount.getAccountDocument().getGoldenRecordId());
                  contact.setAccountId(existingAccount.getAccountDocument().getId());

                  contact.setNeedsSync(true);
                  contact.setEmailAddress(contactDto.getEmail());
                  contact.setPhoneNumber(contactDto.getPhoneNumber());
                  contact.setLastModifiedTimeUtc(new DateEpoch(Instant.now()));
                  contact.setLastSyncTimeUtc(Instant.now());
                  contact.setLastUpdateDateTime(new DateEpoch(Instant.now()));
                  contact.setLastModifyUser(userServiceImpl.getCurrentLoggedInUser());
                  contact.setFirstName(contactDto.getFirstName());
                  contact.setLastName(contactDto.getLastName());
                }
              });
    }
  }

  private DataSource findDataSource(String countryId) {
    Set<Business> liftCountries =
        Set.of(Business.US, Business.Global, Business.Brazil, Business.Canada, Business.Mexico);
    if (countryId != null && liftCountries.contains(Business.valueOf(countryId))) {
      return DataSource.LIFT;
    } else {
      return DataSource.CRESCENDO;
    }
  }
}
