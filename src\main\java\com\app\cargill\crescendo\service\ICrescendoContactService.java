/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service;

import com.app.cargill.document.Contact;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;

public interface ICrescendoContactService {

  String createContact(
      Contact contact, Locale locale, ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions;

  String updateContact(
      Contact contact, Locale locale, ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions;
}
