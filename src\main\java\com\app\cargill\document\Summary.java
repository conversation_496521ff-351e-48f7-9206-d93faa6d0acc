/* Cargill Inc.(C) 2022 */
package com.app.cargill.document;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@SuppressWarnings("java:S125")
public class Summary implements Serializable {

  /** */
  private static final long serialVersionUID = 1L;

  @JsonProperty("HerdBaseline")
  private HerdBaseline herdBaseline;

  @JsonProperty("Quota")
  private Quota quota;

  @JsonProperty("MilkProduction")
  private MilkProductionSummary milkProduction;

  @JsonProperty("Revenue")
  private Revenue revenue;

  @JsonProperty("FeedCosts")
  private FeedCosts feedCosts;

  @JsonProperty("CurrentReturnOverFeedCosts")
  private CurrentReturnOverFeedCosts currentReturnOverFeedCosts;

  @JsonProperty("PreviousReturnOverFeedCosts")
  private PreviousReturnOverFeedCosts previousReturnOverFeedCosts;

  @JsonProperty("FeedingKgDMPerDay")
  private FeedKgDmPerDay feedingKgDMPerDay;
}
