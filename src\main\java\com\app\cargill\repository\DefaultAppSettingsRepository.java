/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.DefaultAppSettings;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface DefaultAppSettingsRepository extends JpaRepository<DefaultAppSettings, Long> {

  @Query(
      value =
          "Select u.* from default_app_settings u where"
              + " LOWER(u.default_app_setting_document->>'CountryId')=LOWER(:country) AND u.deleted"
              + " = false",
      nativeQuery = true)
  DefaultAppSettings findDefaultAppSettingsByCountry(@Param("country") String country);
}
