/* Cargill Inc.(C) 2022 */
package com.app.cargill.dto;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

class ReturnOverFeedPerKgFatDataPointsTest {

  @Test
  void testBuilder_shouldCreateValidDataPoints() {
    // Given
    Double rofPerKgButterFat = 15.75;
    Double concentrateCostPerKgButterFat = 8.20;
    Double totalRevenueDollarKgPerFat = 24.95;
    Double totalCostsPerKgPerFat = 9.20;
    String visitDate = "Week 1";

    // When
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(rofPerKgButterFat)
            .concentrateCostPerKgButterFat(concentrateCostPerKgButterFat)
            .totalRevenueDollarKgPerFat(totalRevenueDollarKgPerFat)
            .totalCostsPerKgPerFat(totalCostsPerKgPerFat)
            .visitDate(visitDate)
            .build();

    // Then
    assertNotNull(dataPoints);
    assertEquals(rofPerKgButterFat, dataPoints.getRofPerKgButterFat());
    assertEquals(concentrateCostPerKgButterFat, dataPoints.getConcentrateCostPerKgButterFat());
    assertEquals(totalRevenueDollarKgPerFat, dataPoints.getTotalRevenueDollarKgPerFat());
    assertEquals(totalCostsPerKgPerFat, dataPoints.getTotalCostsPerKgPerFat());
    assertEquals(visitDate, dataPoints.getVisitDate());
  }

  @Test
  void testNoArgsConstructor_shouldCreateEmptyDataPoints() {
    // When
    ReturnOverFeedPerKgFatDataPoints dataPoints = new ReturnOverFeedPerKgFatDataPoints();

    // Then
    assertNotNull(dataPoints);
    assertNull(dataPoints.getRofPerKgButterFat());
    assertNull(dataPoints.getConcentrateCostPerKgButterFat());
    assertNull(dataPoints.getTotalRevenueDollarKgPerFat());
    assertNull(dataPoints.getTotalCostsPerKgPerFat());
    assertNull(dataPoints.getVisitDate());
  }

  @Test
  void testAllArgsConstructor_shouldCreateValidDataPoints() {
    // Given
    Double rofPerKgButterFat = 16.50;
    Double concentrateCostPerKgButterFat = 7.80;
    Double totalRevenueDollarKgPerFat = 25.30;
    Double totalCostsPerKgPerFat = 8.80;
    String visitDate = "Week 2";

    // When
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        new ReturnOverFeedPerKgFatDataPoints(
            rofPerKgButterFat,
            concentrateCostPerKgButterFat,
            totalRevenueDollarKgPerFat,
            totalCostsPerKgPerFat,
            visitDate);

    // Then
    assertNotNull(dataPoints);
    assertEquals(rofPerKgButterFat, dataPoints.getRofPerKgButterFat());
    assertEquals(concentrateCostPerKgButterFat, dataPoints.getConcentrateCostPerKgButterFat());
    assertEquals(totalRevenueDollarKgPerFat, dataPoints.getTotalRevenueDollarKgPerFat());
    assertEquals(totalCostsPerKgPerFat, dataPoints.getTotalCostsPerKgPerFat());
    assertEquals(visitDate, dataPoints.getVisitDate());
  }

  @Test
  void testSettersAndGetters_shouldWorkCorrectly() {
    // Given
    ReturnOverFeedPerKgFatDataPoints dataPoints = new ReturnOverFeedPerKgFatDataPoints();
    Double rofPerKgButterFat = 18.25;
    Double concentrateCostPerKgButterFat = 9.10;
    Double totalRevenueDollarKgPerFat = 27.35;
    Double totalCostsPerKgPerFat = 9.10;
    String visitDate = "Week 3";

    // When
    dataPoints.setRofPerKgButterFat(rofPerKgButterFat);
    dataPoints.setConcentrateCostPerKgButterFat(concentrateCostPerKgButterFat);
    dataPoints.setTotalRevenueDollarKgPerFat(totalRevenueDollarKgPerFat);
    dataPoints.setTotalCostsPerKgPerFat(totalCostsPerKgPerFat);
    dataPoints.setVisitDate(visitDate);

    // Then
    assertEquals(rofPerKgButterFat, dataPoints.getRofPerKgButterFat());
    assertEquals(concentrateCostPerKgButterFat, dataPoints.getConcentrateCostPerKgButterFat());
    assertEquals(totalRevenueDollarKgPerFat, dataPoints.getTotalRevenueDollarKgPerFat());
    assertEquals(totalCostsPerKgPerFat, dataPoints.getTotalCostsPerKgPerFat());
    assertEquals(visitDate, dataPoints.getVisitDate());
  }

  @Test
  void testEquals_shouldReturnTrueForSameContent() {
    // Given
    ReturnOverFeedPerKgFatDataPoints dataPoints1 =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(15.75)
            .concentrateCostPerKgButterFat(8.20)
            .totalRevenueDollarKgPerFat(24.95)
            .totalCostsPerKgPerFat(9.20)
            .visitDate("Week 1")
            .build();

    ReturnOverFeedPerKgFatDataPoints dataPoints2 =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(15.75)
            .concentrateCostPerKgButterFat(8.20)
            .totalRevenueDollarKgPerFat(24.95)
            .totalCostsPerKgPerFat(9.20)
            .visitDate("Week 1")
            .build();

    // When & Then
    assertEquals(dataPoints1, dataPoints2);
    assertEquals(dataPoints1.hashCode(), dataPoints2.hashCode());
  }

  @Test
  void testEquals_shouldReturnFalseForDifferentContent() {
    // Given
    ReturnOverFeedPerKgFatDataPoints dataPoints1 =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(15.75)
            .visitDate("Week 1")
            .build();

    ReturnOverFeedPerKgFatDataPoints dataPoints2 =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(16.50)
            .visitDate("Week 2")
            .build();

    // When & Then
    assertNotEquals(dataPoints1, dataPoints2);
    assertNotEquals(dataPoints1.hashCode(), dataPoints2.hashCode());
  }

  @Test
  void testToString_shouldContainAllFields() {
    // Given
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(15.75)
            .concentrateCostPerKgButterFat(8.20)
            .totalRevenueDollarKgPerFat(24.95)
            .totalCostsPerKgPerFat(9.20)
            .visitDate("Week 1")
            .build();

    // When
    String toString = dataPoints.toString();

    // Then
    assertNotNull(toString);
    assertTrue(toString.contains("rofPerKgButterFat"));
    assertTrue(toString.contains("concentrateCostPerKgButterFat"));
    assertTrue(toString.contains("totalRevenueDollarKgPerFat"));
    assertTrue(toString.contains("totalCostsPerKgPerFat"));
    assertTrue(toString.contains("visitDate"));
    assertTrue(toString.contains("15.75"));
    assertTrue(toString.contains("8.2"));
    assertTrue(toString.contains("24.95"));
    assertTrue(toString.contains("9.2"));
    assertTrue(toString.contains("Week 1"));
  }

  @Test
  void testWithNullValues_shouldHandleNullsCorrectly() {
    // Given & When
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(null)
            .concentrateCostPerKgButterFat(null)
            .totalRevenueDollarKgPerFat(null)
            .totalCostsPerKgPerFat(null)
            .visitDate(null)
            .build();

    // Then
    assertNotNull(dataPoints);
    assertNull(dataPoints.getRofPerKgButterFat());
    assertNull(dataPoints.getConcentrateCostPerKgButterFat());
    assertNull(dataPoints.getTotalRevenueDollarKgPerFat());
    assertNull(dataPoints.getTotalCostsPerKgPerFat());
    assertNull(dataPoints.getVisitDate());
  }

  @Test
  void testWithZeroValues_shouldHandleZerosCorrectly() {
    // Given
    Double zeroValue = 0.0;
    String visitDate = "Week 0";

    // When
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(zeroValue)
            .concentrateCostPerKgButterFat(zeroValue)
            .totalRevenueDollarKgPerFat(zeroValue)
            .totalCostsPerKgPerFat(zeroValue)
            .visitDate(visitDate)
            .build();

    // Then
    assertNotNull(dataPoints);
    assertEquals(zeroValue, dataPoints.getRofPerKgButterFat());
    assertEquals(zeroValue, dataPoints.getConcentrateCostPerKgButterFat());
    assertEquals(zeroValue, dataPoints.getTotalRevenueDollarKgPerFat());
    assertEquals(zeroValue, dataPoints.getTotalCostsPerKgPerFat());
    assertEquals(visitDate, dataPoints.getVisitDate());
  }

  @Test
  void testWithNegativeValues_shouldHandleNegativesCorrectly() {
    // Given
    Double negativeValue = -5.25;
    String visitDate = "Week -1";

    // When
    ReturnOverFeedPerKgFatDataPoints dataPoints =
        ReturnOverFeedPerKgFatDataPoints.builder()
            .rofPerKgButterFat(negativeValue)
            .concentrateCostPerKgButterFat(negativeValue)
            .totalRevenueDollarKgPerFat(negativeValue)
            .totalCostsPerKgPerFat(negativeValue)
            .visitDate(visitDate)
            .build();

    // Then
    assertNotNull(dataPoints);
    assertEquals(negativeValue, dataPoints.getRofPerKgButterFat());
    assertEquals(negativeValue, dataPoints.getConcentrateCostPerKgButterFat());
    assertEquals(negativeValue, dataPoints.getTotalRevenueDollarKgPerFat());
    assertEquals(negativeValue, dataPoints.getTotalCostsPerKgPerFat());
    assertEquals(visitDate, dataPoints.getVisitDate());
  }

  @Test
  void testJsonIgnorePropertiesAnnotation_shouldIgnoreUnknownProperties() {
    // This test verifies that the @JsonIgnoreProperties annotation is present
    // The actual JSON deserialization would be tested in integration tests

    // Given
    ReturnOverFeedPerKgFatDataPoints dataPoints = new ReturnOverFeedPerKgFatDataPoints();

    // When & Then
    assertNotNull(dataPoints);
    // The annotation should prevent Jackson from throwing exceptions for unknown properties
    // This is more of a compile-time check that the annotation is present
  }
}
