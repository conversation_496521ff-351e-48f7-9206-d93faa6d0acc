/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service;

import com.app.cargill.crescendo.model.EventsCrescendo;
import com.app.cargill.exceptions.CustomDEExceptions;
import java.util.Locale;
import org.springframework.context.support.ResourceBundleMessageSource;

public interface ICrescendoActivityService {

  String createActivity(
      EventsCrescendo eventsCrescendo,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions;

  String updateActivity(
      EventsCrescendo eventsCrescendo,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions;
}
