<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="041" author="Nivetha">
		<sql>
            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323211', now(),
            '{ "CountryId": "Global",
            "BCSPointScale": "HalfPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "USD"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323212', now(),
            '{ "CountryId": "Brazil",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "BRL"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323213', now(),
            '{ "CountryId": "Canada",
            "BCSPointScale": "HalfPointScale", "UnitOfMeasure": "Imperial", "DefaultCurrency": "CAD"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323214', now(),
            '{ "CountryId": "Netherlands",
            "BCSPointScale": "HalfPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323215', now(),
            '{ "CountryId": "Poland",
            "BCSPointScale": "HalfPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323216', now(),
            '{ "CountryId": "US",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Imperial", "DefaultCurrency": "USD"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323217', now(),
            '{ "CountryId": "Italy",
            "BCSPointScale": "HalfPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323218', now(),
            '{ "CountryId": "India",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "INR"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323219', now(),
            '{ "CountryId": "Russia",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "RUB"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323220', now(),
            '{ "CountryId": "SouthAfrica",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "ZAR"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323221', now(),
            '{ "CountryId": "China",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "CNY"}'::jsonb);


            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323222', now(),
            '{ "CountryId": "Portugal",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323223', now(),
            '{ "CountryId": "Ukraine",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "UAH"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323224', now(),
            '{ "CountryId": "Hungary",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323225', now(),
            '{ "CountryId": "Argentina",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "ARS"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323226', now(),
            '{ "CountryId": "Neolait_FRANCE",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);

            INSERT INTO public.default_app_settings(
            created_date, deleted, local_id, updated_date, default_app_setting_document)
            VALUES (now(), false, '232323227', now(),
            '{ "CountryId": "Provimi_FRANCE",
            "BCSPointScale": "QuarterPointScale", "UnitOfMeasure": "Metric", "DefaultCurrency": "Euro"}'::jsonb);
			
		</sql>
	</changeSet>

</databaseChangeLog>