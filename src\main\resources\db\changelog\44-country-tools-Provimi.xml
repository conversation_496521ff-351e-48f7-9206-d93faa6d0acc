<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

	<changeSet id="044" author="Nivetha">
	 <sql splitStatements="false">

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '9508d694-cdbb-442e-8c56-b26a8c2a35d6', '2025-07-21 11:39:46.023', '{"id": "9508d694-cdbb-442e-8c56-b26a8c2a35d6", "IsNew": false, "ToolId": "HeatStress", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '7a4d4a35-2e8f-4584-8546-610ac7b9a651', '2025-07-21 11:39:46.023', '{"id": "7a4d4a35-2e8f-4584-8546-610ac7b9a651", "IsNew": false, "ToolId": "PenTimeBudgetTool", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Comfort", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'd5987136-f11d-4487-880e-4a5e7d1da7e1', '2025-07-21 11:39:46.023', '{"id": "d5987136-f11d-4487-880e-4a5e7d1da7e1", "IsNew": false, "ToolId": "RumenHealth", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'b9b8fc14-f4b9-449c-86f6-0826d23a5488', '2025-07-21 11:39:46.023', '{"id": "b9b8fc14-f4b9-449c-86f6-0826d23a5488", "IsNew": false, "ToolId": "RumenHealthManureScore", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '514b83f7-d104-40de-80d4-ae5d9689bd9d', '2025-07-21 11:39:46.023', '{"id": "514b83f7-d104-40de-80d4-ae5d9689bd9d", "IsNew": false, "ToolId": "ManureScreener", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'b53e67f4-0f80-4f01-befc-29ab87bb052b', '2025-07-21 11:39:46.023', '{"id": "b53e67f4-0f80-4f01-befc-29ab87bb052b", "IsNew": false, "ToolId": "RumenFill", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'ab01272b-7835-4bae-bf26-964e966fe464', '2025-07-21 11:39:46.023', '{"id": "ab01272b-7835-4bae-bf26-964e966fe464", "IsNew": false, "ToolId": "LocomotionScore", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'cfcf2efa-4ebf-4ab6-8bd9-b0cb4f839f14', '2025-07-21 11:39:46.023', '{"id": "cfcf2efa-4ebf-4ab6-8bd9-b0cb4f839f14", "IsNew": false, "ToolId": "BodyCondition", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '35fc8da6-d238-4ca1-9828-2ef4a72363aa', '2025-07-21 11:39:46.023', '{"id": "35fc8da6-d238-4ca1-9828-2ef4a72363aa", "IsNew": false, "ToolId": "MetabolicIncidence", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'e2f4352f-d4bf-4c05-96de-bd835ca9a9e2', '2025-07-21 11:39:46.023', '{"id": "e2f4352f-d4bf-4c05-96de-bd835ca9a9e2", "IsNew": false, "ToolId": "ForageAuditScorecard", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'a3bc2c41-49f6-4abb-8c5d-b0c3bb4d9c04', '2025-07-21 11:39:46.023', '{"id": "a3bc2c41-49f6-4abb-8c5d-b0c3bb4d9c04", "IsNew": false, "ToolId": "PileAndBunker", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'fc288f49-9a80-43de-b913-5af889c86ca4', '2025-07-21 11:39:46.023', '{"id": "fc288f49-9a80-43de-b913-5af889c86ca4", "IsNew": false, "ToolId": "MilkSoldEvaluation", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);


		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '811b495c-a440-4a33-bc07-dc52669f408d', '2025-07-21 11:39:46.023', '{"id": "811b495c-a440-4a33-bc07-dc52669f408d", "IsNew": false, "ToolId": "RoboticMilkEvaluation", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '86aa08d6-f58a-4f34-8357-9811283258ad', '2025-07-21 11:39:46.023', '{"id": "86aa08d6-f58a-4f34-8357-9811283258ad", "IsNew": false, "ToolId": "Revenue", "CountryId": "Provimi_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Productivity", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, '73428271-58af-4c9d-b4ed-f84a2b7270cd', '2025-07-21 11:39:46.023', '{"id": "73428271-58af-4c9d-b4ed-f84a2b7270cd", "IsNew": false, "ToolId": "UrinePHTool", "CountryId": "Provimi_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'eeb68cc1-e04b-4137-ae09-9a97d492b8f5', '2025-07-21 11:39:46.023', '{"id": "eeb68cc1-e04b-4137-ae09-9a97d492b8f5", "IsNew": false, "ToolId": "ReadyToMilk", "CountryId": "Provimi_FRANCE", "IsDeleted": true, "CreateUser": null, "ToolGroupId": "Health", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'ec2a49c9-5a5e-4c2f-8abe-7d4dcb812c31', '2025-07-21 11:39:46.023', '{"id": "ec2a49c9-5a5e-4c2f-8abe-7d4dcb812c31", "IsNew": false, "ToolId": "CalfHeiferScorecard", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "CalfandHeifer", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);

		 INSERT INTO public.country_tools
		 (created_date, deleted, local_id, updated_date, country_tool_document)
		 VALUES('2025-07-21 11:39:46.023', false, 'd84b9a8c-128a-43a9-be53-5fd53ec4985d', '2025-07-21 11:39:46.023', '{"id": "d84b9a8c-128a-43a9-be53-5fd53ec4985d", "IsNew": false, "ToolId": "TMRParticleScore", "CountryId": "Provimi_FRANCE", "IsDeleted": false, "CreateUser": null, "ToolGroupId": "Nutrition", "CreateTimeUtc": "2025-07-21T03:30:29.906973500Z", "LastModifyUser": null, "LastSyncTimeUtc": null, "LastModifiedTimeUtc": "2025-07-21 11:39:46.023"}'::jsonb);
</sql>


	</changeSet>

</databaseChangeLog>