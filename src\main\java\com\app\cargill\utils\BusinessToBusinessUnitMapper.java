/* Cargill Inc.(C) 2022 */
package com.app.cargill.utils;

import com.app.cargill.constants.Business;
import com.app.cargill.crescendo.constants.BusinessUnitCrescendo;

public class BusinessToBusinessUnitMapper {

  private BusinessToBusinessUnitMapper() {}

  public static BusinessUnitCrescendo toBusinessUnit(Business business) {
    return switch (business) {
      case Global -> null;
      case Brazil -> BusinessUnitCrescendo.BRAZIL;
      case Canada -> BusinessUnitCrescendo.CANADA;
      case France -> BusinessUnitCrescendo.FRANCE;
      case Netherlands -> BusinessUnitCrescendo.NETHERLANDS;
      case Philippines -> BusinessUnitCrescendo.PHILIPPINES;
      case Poland -> BusinessUnitCrescendo.POLAND;
      case US -> BusinessUnitCrescendo.US;
      case Vietnam -> BusinessUnitCrescendo.VIETNAM;
      case Spain -> BusinessUnitCrescendo.SPAIN;
      case Italy -> BusinessUnitCrescendo.ITALY;
      case Korea -> BusinessUnitCrescendo.KOREA;
      case India -> BusinessUnitCrescendo.INDIA;
      case Mexico -> BusinessUnitCrescendo.MEXICO;
      case Russia -> BusinessUnitCrescendo.RUSSIA;
      case SouthAfrica -> BusinessUnitCrescendo.SOUTH_AFRICA;
      case CPNBrazil -> BusinessUnitCrescendo.CPN_BRAZIL;
      case CPNFrance -> BusinessUnitCrescendo.CPN_FRANCE;
      case NorthAmerica -> BusinessUnitCrescendo.NORTH_AMERICA;
      case China -> BusinessUnitCrescendo.CHINA;
      case Portugal -> BusinessUnitCrescendo.PORTUGAL;
      case Ukraine -> BusinessUnitCrescendo.UKRAINE;
      case CFNChina -> BusinessUnitCrescendo.CFN_CHINA;
      case CFNIndia -> BusinessUnitCrescendo.CFN_INDIA;
      case CPNPoland -> BusinessUnitCrescendo.CPN_POLAND;
      case CPNUS -> BusinessUnitCrescendo.CPN_US;
      case Hungary -> BusinessUnitCrescendo.HUNGARY;
      case UK -> BusinessUnitCrescendo.UNITED_KINGDOM;
      case Pakistan -> BusinessUnitCrescendo.PAKISTAN;
      case UNITED_STATES -> BusinessUnitCrescendo.UNITED_STATES;
      case Argentina -> BusinessUnitCrescendo.ARGENTINA;
      case Neolait_FRANCE -> BusinessUnitCrescendo.NEOLAIT_FRANCE;
      case Provimi_FRANCE -> BusinessUnitCrescendo.PROVIMI_FRANCE;
    };
  }

  public static Business toBusiness(BusinessUnitCrescendo business) {
    if (business == null) {
      return null;
    }
    return switch (business) {
      case BRAZIL -> Business.Brazil;
      case CANADA -> Business.Canada;
      case FRANCE -> Business.France;
      case NETHERLANDS -> Business.Netherlands;
      case PHILIPPINES -> Business.Philippines;
      case POLAND -> Business.Poland;
      case US -> Business.US;
      case VIETNAM -> Business.Vietnam;
      case SPAIN -> Business.Spain;
      case ITALY -> Business.Italy;
      case KOREA -> Business.Korea;
      case INDIA -> Business.India;
      case MEXICO -> Business.Mexico;
      case RUSSIA -> Business.Russia;
      case SOUTH_AFRICA -> Business.SouthAfrica;
      case CPN_BRAZIL -> Business.CPNBrazil;
      case CPN_FRANCE -> Business.CPNFrance;
      case NORTH_AMERICA -> Business.NorthAmerica;
      case CHINA -> Business.China;
      case PORTUGAL -> Business.Portugal;
      case UKRAINE -> Business.Ukraine;
      case CFN_CHINA -> Business.CFNChina;
      case CFN_INDIA -> Business.CFNIndia;
      case CPN_POLAND -> Business.CPNPoland;
      case CPN_US -> Business.CPNUS;
      case HUNGARY -> Business.Hungary;
      case UNITED_KINGDOM -> Business.UK;
      case PAKISTAN -> Business.Pakistan;
      case UNITED_STATES -> Business.UNITED_STATES;
      case ARGENTINA -> Business.Argentina;
      case NEOLAIT_FRANCE -> Business.Neolait_FRANCE;
      case PROVIMI_FRANCE -> Business.Provimi_FRANCE;
    };
  }
}
