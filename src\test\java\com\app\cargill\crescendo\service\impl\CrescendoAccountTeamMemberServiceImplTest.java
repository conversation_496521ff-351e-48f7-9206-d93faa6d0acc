/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.app.cargill.crescendo.model.AccountTeamMember;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;

@ExtendWith(MockitoExtension.class)
class CrescendoAccountTeamMemberServiceImplTest {

  @Mock private CrescendoApiServiceImpl crescendoApiServiceImpl;

  @Mock private ResourceBundleMessageSource bundleMessageSource;

  @InjectMocks private CrescendoAccountTeamMemberServiceImpl service;

  private AccountTeamMember testAccountTeamMember;
  private AuthToken authToken;
  private AccessTokenAndApiPathDto tokenAndApiPath;
  private CreateRecordResponse createRecordResponse;

  @BeforeEach
  void setUp() {
    testAccountTeamMember = createTestAccountTeamMember();
    authToken = createTestAuthToken();
    tokenAndApiPath =
        AccessTokenAndApiPathDto.builder()
            .authToken(authToken)
            .apiPath("https://test.salesforce.com/services/data/v58.0")
            .build();

    createRecordResponse = new CreateRecordResponse();
    createRecordResponse.setId("001XXXXXXXXXXXXXXX");
    createRecordResponse.setSuccess(true);
  }

  @Test
  void createAccountTeamMember_Success() throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(AccountTeamMember.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    String result =
        service.createAccountTeamMember(testAccountTeamMember, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            eq(testAccountTeamMember),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/AccountTeamMember"));
  }

  @Test
  void createAccountTeamMember_WithNullLocaleAndSource()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(AccountTeamMember.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    String result = service.createAccountTeamMember(testAccountTeamMember, null, null);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            eq(testAccountTeamMember),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/AccountTeamMember"));
  }

  @Test
  void createAccountTeamMember_ApiServiceException()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(AccountTeamMember.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenThrow(new RuntimeException("API Error"));

    // Act & Assert
    CustomDEExceptions exception =
        assertThrows(
            CustomDEExceptions.class,
            () ->
                service.createAccountTeamMember(
                    testAccountTeamMember, Locale.ENGLISH, bundleMessageSource));

    assertEquals("API Error", exception.getMessage());
    assertEquals(HttpStatus.SC_FORBIDDEN, exception.getStatusCode());
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl).createRecord(any(), any(), any(), any());
  }

  @Test
  void createAccountTeamMember_GetTokenException()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath())
        .thenThrow(new RuntimeException("Token retrieval failed"));

    // Act & Assert
    RuntimeException exception =
        assertThrows(
            RuntimeException.class,
            () ->
                service.createAccountTeamMember(
                    testAccountTeamMember, Locale.ENGLISH, bundleMessageSource));

    assertEquals("Token retrieval failed", exception.getMessage());
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl, never()).createRecord(any(), any(), any(), any());
  }

  @Test
  void createAccountTeamMember_WithNullAccountTeamMember()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class), any(), any(ParameterizedTypeReference.class), anyString()))
        .thenReturn(createRecordResponse);

    // Act
    String result = service.createAccountTeamMember(null, Locale.ENGLISH, bundleMessageSource);

    // Assert
    assertNotNull(result);
    assertEquals("001XXXXXXXXXXXXXXX", result);
    verify(crescendoApiServiceImpl).getTokenAndApiPath();
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            isNull(),
            any(ParameterizedTypeReference.class),
            contains("/sobjects/AccountTeamMember"));
  }

  @Test
  void createAccountTeamMember_VerifyUrlFormat()
      throws CustomDEExceptions, JsonProcessingException {
    // Arrange
    when(crescendoApiServiceImpl.getTokenAndApiPath()).thenReturn(tokenAndApiPath);
    when(crescendoApiServiceImpl.createRecord(
            any(AuthToken.class),
            any(AccountTeamMember.class),
            any(ParameterizedTypeReference.class),
            anyString()))
        .thenReturn(createRecordResponse);

    // Act
    service.createAccountTeamMember(testAccountTeamMember, Locale.ENGLISH, bundleMessageSource);

    // Assert
    verify(crescendoApiServiceImpl)
        .createRecord(
            eq(authToken),
            eq(testAccountTeamMember),
            any(ParameterizedTypeReference.class),
            eq("https://test.salesforce.com/services/data/v58.0/sobjects/AccountTeamMember"));
  }

  private AccountTeamMember createTestAccountTeamMember() {
    AccountTeamMember accountTeamMember = new AccountTeamMember();
    accountTeamMember.setId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setUserId("005XXXXXXXXXXXXXXX");
    accountTeamMember.setAccountId("001XXXXXXXXXXXXXXX");
    accountTeamMember.setTeamMemberRole("Account Manager");
    accountTeamMember.setAccountAccessLevel("Edit");
    accountTeamMember.setOpportunityAccessLevel("Edit");
    accountTeamMember.setCaseAccessLevel("Edit");
    accountTeamMember.setContactAccessLevel("Edit");
    return accountTeamMember;
  }

  private AuthToken createTestAuthToken() {
    AuthToken token = new AuthToken();
    token.setAccessToken("test_access_token");
    token.setInstanceUrl("https://test.salesforce.com");
    token.setTokenType("Bearer");
    return token;
  }
}
