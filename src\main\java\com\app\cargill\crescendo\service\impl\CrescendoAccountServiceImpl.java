/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.service.impl;

import com.app.cargill.constants.Business;
import com.app.cargill.crescendo.constants.AccountTypeCrescendo;
import com.app.cargill.crescendo.constants.NineBoxStepTwoIDCrescendo;
import com.app.cargill.crescendo.constants.SegmentStepOneIdCrescendo;
import com.app.cargill.crescendo.constants.SubTypeIdCrescendo;
import com.app.cargill.crescendo.model.AccountsCrescendo;
import com.app.cargill.crescendo.model.Users;
import com.app.cargill.crescendo.service.ICrescendoAccountService;
import com.app.cargill.crescendo.service.ICrescendoUserService;
import com.app.cargill.document.AccountDocument;
import com.app.cargill.dto.AccessTokenAndApiPathDto;
import com.app.cargill.exceptions.CustomDEExceptions;
import com.app.cargill.sf.cc.model.AuthToken;
import com.app.cargill.sf.cc.model.CreateRecordResponse;
import com.app.cargill.sf.cc.model.SalesforceRecordsResponse;
import com.app.cargill.utils.BusinessToBusinessUnitMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import java.util.Locale;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
@Slf4j
@SuppressWarnings(
    "java:S1172") // Added for unused variables, which will be incorporated during translations
// integration
public class CrescendoAccountServiceImpl implements ICrescendoAccountService {

  private final CrescendoApiServiceImpl crescendoApiService;
  private final ICrescendoUserService crescendoUserService;

  @Override
  public String createAccount(
      AccountDocument accountDocument, Locale locale, ResourceBundleMessageSource source)
      throws CustomDEExceptions, JsonProcessingException {
    AccessTokenAndApiPathDto token = crescendoApiService.getTokenAndApiPath();
    return createAccount(token.getApiPath(), token.getAuthToken(), accountDocument, source, locale);
  }

  private String createAccount(
      String apiPath,
      AuthToken authToken,
      AccountDocument accountDocument,
      ResourceBundleMessageSource source,
      Locale locale)
      throws CustomDEExceptions, JsonProcessingException {

    try {
      String accountUrl = String.format("%s/sobjects/Account", apiPath);
      SalesforceRecordsResponse<Users> user =
          crescendoUserService.getByUserEmail(accountDocument.getOwnerId());
      String userId = user.getRecords().get(0).getId();
      log.info(
          "CREATE_CRESCENDO_ACCOUNT_OWNER email: {} id: {}", accountDocument.getOwnerId(), userId);

      AccountsCrescendo account = documentToCrescendo(accountDocument, userId);
      log.info("CREATED CRESCENDO ACCOUNT STARTED {}", account);
      CreateRecordResponse recordResponse =
          crescendoApiService.createRecord(
              authToken, account, new ParameterizedTypeReference<>() {}, accountUrl);
      log.info("CRESCENDO_ACCOUNT_CREATED {}", recordResponse.getId());
      accountDocument.setGoldenRecordId(recordResponse.getId());
      return recordResponse.getId();
    } catch (Exception ex) {
      log.error(
          "CRESCENDO_ACCOUNTS_CREATE_ERROR {} , {}", accountDocument.getId(), ex.getMessage());
      throw new CustomDEExceptions(ex.getLocalizedMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }

  @Override
  public String updateAccount(
      AccountDocument accountDocument,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {
    AccessTokenAndApiPathDto token = crescendoApiService.getTokenAndApiPath();

    return updateAccount(
        token.getAuthToken(), token.getApiPath(), accountDocument, locale, bundleMessageSource);
  }

  public String updateAccount(
      AuthToken authToken,
      String apiPath,
      AccountDocument accountDocument,
      Locale locale,
      ResourceBundleMessageSource bundleMessageSource)
      throws CustomDEExceptions {

    try {
      String accountUrl =
          String.format("%s/sobjects/Account/%s", apiPath, accountDocument.getGoldenRecordId());
      SalesforceRecordsResponse<Users> user =
          crescendoUserService.getByUserEmail(accountDocument.getOwnerId());
      String userId = user.getRecords().get(0).getId();
      log.info(
          "UPDATE_CRESCENDO_ACCOUNT_OWNER email: {} id: {}", accountDocument.getOwnerId(), userId);
      AccountsCrescendo account = documentToCrescendo(accountDocument, userId);

      log.info("UPDATE_CRESCENDO_ACCOUNT {}", account);
      crescendoApiService.updateRecord(
          authToken, account, new ParameterizedTypeReference<>() {}, accountUrl);
      return account.getGoldenRecordName();
    } catch (Exception ex) {
      log.error(
          "Error updating Crescendo account: {} , {}", accountDocument.getId(), ex.getMessage());
      throw new CustomDEExceptions(ex.getMessage(), HttpStatus.SC_FORBIDDEN);
    }
  }

  private AccountsCrescendo documentToCrescendo(AccountDocument accountDocument, String userId) {
    AccountsCrescendo account = new AccountsCrescendo();

    account.setAccountType(
        AccountTypeCrescendo.getFromInt(accountDocument.getAccountType()).getValue());
    account.setActive(true);
    account.setAutoValidate(true);
    if (Objects.nonNull(accountDocument.getPhysicalAddress())) {
      account.setBillingCity(accountDocument.getPhysicalAddress().getCity());
      account.setBillingCountry(accountDocument.getPhysicalAddress().getCountry());
      account.setBillingPostalCode(accountDocument.getPhysicalAddress().getPostalCode());
      account.setBillingState(accountDocument.getPhysicalAddress().getStateOrProvince());
    }

    Business businessId = Business.fromId(accountDocument.getBusinessID());
    if (businessId != null) {
      account.setBusiness(BusinessToBusinessUnitMapper.toBusinessUnit(businessId).getValue());
      account.setBusinessUnit(BusinessToBusinessUnitMapper.toBusinessUnit(businessId).getValue());
    }
    account.setCustomerCode(accountDocument.getCustomerCode());
    account.setExternalId(accountDocument.getId().toString());
    account.setIsDuplicate(false);
    account.setMobileFirst(true);
    account.setName(accountDocument.getAccountName());
    account.setOwnerId(userId);
    account.setProspectValidated(false);
    account.setRecordTypeId(accountDocument.getType());
    if (accountDocument.getSegmentStepOneId() != null
        && !accountDocument.getSegmentStepOneId().isEmpty()) {
      account.setSegmentStep1(
          SegmentStepOneIdCrescendo.fromValue(accountDocument.getSegmentStepOneId()).getValue());
    } else {
      account.setSegmentStep1(SegmentStepOneIdCrescendo.NOAH.getValue());
    }
    account.setSourceSystem("LM");
    account.setType(SubTypeIdCrescendo.FARM_PRODUCER.getValue());
    if (accountDocument.getNineBoxStepTwoID() != null) {
      account.setX9BoxStep2(
          NineBoxStepTwoIDCrescendo.fromString(accountDocument.getNineBoxStepTwoID()).getValue());
    }

    return account;
  }
}
