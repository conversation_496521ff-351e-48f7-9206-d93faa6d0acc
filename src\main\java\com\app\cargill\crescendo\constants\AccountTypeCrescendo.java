/* Cargill Inc.(C) 2022 */
package com.app.cargill.crescendo.constants;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

public enum AccountTypeCrescendo {
  STANDARD("Standard"),
  COMMERCIAL_CUSTOMER("Commercial Customer"),
  THIRD_PARTY("Third Party"),
  CONSUMER("Consumer"),
  COMPETITOR("Competitor"),
  COMMERCIAL("Commercial");

  private final String value;

  @JsonCreator
  AccountTypeCrescendo(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  public static AccountTypeCrescendo getFromInt(Integer index) {
    AccountTypeCrescendo[] values = AccountTypeCrescendo.values();
    if (index == null || index >= values.length) {
      return AccountTypeCrescendo.STANDARD;
    } else {
      return values[index];
    }
  }

  public static Integer toInt(AccountTypeCrescendo type) {
    AccountTypeCrescendo[] values = AccountTypeCrescendo.values();
    if (type == null) {
      return 0;
    } else {
      for (int i = 0; i < values.length; i++) {
        if (values[i] == type) {
          return i;
        }
      }
      return 0;
    }
  }
}
