/* Cargill Inc.(C) 2022 */
package com.app.cargill.repository;

import com.app.cargill.model.UserJourney;
import java.time.Instant;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface UserJourneyRepository
    extends JpaRepository<UserJourney, Long>, JpaSpecificationExecutor<UserJourney> {

  @Query(
      value =
          "SELECT uj.* FROM user_journey uj WHERE timezone('UTC', uj.updated_date) >"
              + " :lastUpdatedDate AND uj.deleted = false",
      nativeQuery = true)
  List<UserJourney> findByUpdatedDateAfter(@Param("lastUpdatedDate") Instant lastUpdatedDate);

  @Query(
      value =
          "SELECT uj.* FROM user_journey uj WHERE uj.user_journey_document ->> 'UserEmail' ="
              + " :userEmail AND uj.deleted = false",
      nativeQuery = true)
  List<UserJourney> findByUserEmail(@Param("userEmail") String userEmail);
}
